<!DOCTYPE html>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link href="../../../../favicon.ico@28d37e6d7e5e12d24c39bdba0f4f3cfd" rel="shortcut icon" type="image/x-icon" />
<link href="../../../../stylesheets/<EMAIL>" rel="stylesheet" type="text/css" />
<title>Architectural Evolution (AgileEAD 2025 - Agile Enterprise Architecture and Delivery) - XP 2025</title>
<script type="text/javascript">var show_webdsl_debug=false;</script>
<script type="text/javascript">var contextpath="https://conf.researchr.org";</script>
<link rel="stylesheet" href="../../../../stylesheets/bootstrap/css/<EMAIL>" type="text/css" /><link rel="stylesheet" href="../../../../stylesheets/<EMAIL>" type="text/css" /><link rel="stylesheet" href="../../../../getFile/02996ded-1ac6-4a2c-8bdf-800f973baf56/<EMAIL>" type="text/css" /><link rel="stylesheet" href="../../../../stylesheets/<EMAIL>" type="text/css" /><link rel="stylesheet" href="../../../../stylesheets/<EMAIL>" type="text/css" /><script type="text/javascript" src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script type="text/javascript" src="../../../../javascript/flatpickr-4.3.2.min.js@c2885c17ae5fd471c9a40c437e8a2736"></script>
<script type="text/javascript" src="../../../../javascript/jquery.tablesorter.combined.min.js@285b21976326641f8f1b910492e6d5d0"></script>
<script type="text/javascript" src="../../../../javascript/jquery.tablesorter.pager.min.js@4551cdfea66f45a11ae5548f3d50a06f"></script>
<script type="text/javascript" src="../../../../javascript/tablesorter-init.js@ee6babedf1a6be960e4543a1bb57b421"></script>
<script type="text/javascript" src="../../../../javascript/bootstrap/js/bootstrap.min.js@2f34b630ffe30ba2ff2b91e3f3c322a1"></script>
<script type="text/javascript" src="../../../../javascript/notify.min.js@e86d399917a84b69a4683f504c7a7813"></script>
<script type="text/javascript" src="../../../../javascript/ajax.js@2dbd1cc4f8f2d52ae9c879d7346f4f8a"></script>
<script type="text/javascript">(function($){
        $(document).ready(function(){
          $('ul.dropdown-menu [data-toggle=dropdown]').on('click', function(event) {
            event.preventDefault(); 
            event.stopPropagation(); 
            $(this).parent().siblings().removeClass('open');
            $(this).parent().toggleClass('open');
          });
        });
      })(jQuery);
      </script>
<script type="text/javascript"> $(window).on('popstate', function(){ $('.modal.in').modal('hide') }); </script><script type="text/javascript">(function(){
    var post_process_function = function(n){ var node=(n&&n.nodeType === 1)?n:document; $(node).find('.modal').on( 'shown.bs.modal', function(){ if(history.pushState){ history.pushState({ id: 'modal', modalId: $(this).attr('id') }, null, window.location.href); } } ).on( 'hidden.bs.modal', function (){ if( history.state != null && history.state.modalId == $(this).attr('id') ){ history.back(); } } ); };
    var original_post_process_func = ajax_post_process;
    ajax_post_process = function(){
      original_post_process_func.apply(this,arguments);
      post_process_function.apply(this,arguments);
    };
    $(document).ready( post_process_function );
  })();   
  </script>
<script>window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag('js', new Date());gtag('config', 'G-4G0QWEKVQS', {'cookie_domain': 'auto', 'anonymizeIp': true });gtag('config', 'UA-9607501-2', {'cookie_domain': 'auto', 'anonymizeIp': true });</script>
<meta  prefix='og: http://ogp.me/ns#' property='og:description' content='Combining agile thinking with proven enterprise architecture strategies can enhance long-term viability and delivery success in complex IT landscapes. Agile is ideal for rapid development and customer-focused features, but in large companies with legacy systems, it can lead to unaligned developments and negative consequences. This has made some IT leaders skeptical of agile. 
Conversely, upfront analysis and design of large systems often fail due to lack of management support and difficulty maintaining focus. The challenge is balancing short-term and long-term architectural design to confi ...'>
<script type="text/javascript">(function(){
    var post_process_function = function(n){ var node=(n&&n.nodeType === 1)?n:document; $(node).find('input.flatpickr:not([id])').focusout( function(){ this.dispatchEvent( new KeyboardEvent('keydown',{keyCode:13, bubbles: true}) ); } ); };
    var original_post_process_func = ajax_post_process;
    ajax_post_process = function(){
      original_post_process_func.apply(this,arguments);
      post_process_function.apply(this,arguments);
    };
    $(document).ready( post_process_function );
  })();   
  </script>
<meta  prefix='og: http://ogp.me/ns#' property='og:image' content='https://conf.researchr.org/getImage/xp-2025/orig/XP-2025.jpg'>
<script type="text/javascript">function addEventModalLoadOnClick( containerNode ){
        $(containerNode).find( "a[data-event-modal]" ).on("click", function(e){
          var eventId = $(this).data("event-modal"); 
          var modalElem = $("#modal-" + eventId);
          if(modalElem.length){
            modalElem.modal({ backdrop: 'static', keyboard: true}, 'show');
          } else {
            var loaderElem = $("#event-modal-loader");
            loaderElem.find("input.event-id-input").val( eventId );
            loaderElem.find("#load-modal-action").click();
          }
          e.preventDefault();
        } );
      }
    </script><script type="text/javascript">function addStarredEventOnClick( containerNode ){
        $(containerNode).find( "[data-event-star]" ).on("click", function(e){
          var eventId = $(this).data("event-star"); 
          var starEventFormElem = $("#event-star-form");
          starEventFormElem.find("input.event-id-input").val( eventId );
          starEventFormElem.find("#star-event-action").click();
          e.preventDefault();
          e.stopPropagation();
        } );
      }
    </script><script type="text/javascript">function pauseOnCloseModal( modalid ){
        //pauses video (only youtube at the moment) when closing modal
        $('#' + modalid).on('hidden.bs.modal', function () {
          $(this).find('.embed-container iframe[src*=enablejsapi]').each( function(){ this.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*') } );
        } )
      }
    </script>
<script type="text/javascript">$(document).ready( function(){
          $('#program-menu a[data-fltr-type]').on('click', function(event){
            event.preventDefault();
            window.location = $(this).attr('href') + "?" + encodeURIComponent( $(this).data('fltr-type') ) + '=' + encodeURIComponent( $(this).data('fltr-val') );
          })
        })
      </script>
<meta prefix='og: http://ogp.me/ns#' property='og:title' content='Architectural Evolution (AgileEAD 2025 - Agile Enterprise Architecture and Delivery) - XP 2025'>
<script async src="https://www.googletagmanager.com/gtag/js?id=G-4G0QWEKVQS"></script>
<meta name='description' content='Combining agile thinking with proven enterprise architecture strategies can enhance long-term viability and delivery success in complex IT landscapes. Agile is ideal for rapid development and customer-focused features, but in large companies with legacy systems, it can lead to unaligned developments and negative consequences. This has made some IT leaders skeptical of agile. 
Conversely, upfront analysis and design of large systems often fail due to lack of management support and difficulty maintaining focus. The challenge is balancing short-term and long-term architectural design to confi ...'>
<!--[if IE 9]><script type="text/javascript" src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"></script><![endif]-->
<meta name='keywords' content='XP, agile, scrum, agile alliance, conference, research, practice'>
</head>
<body id="details"><div class="frame"><div class="logobar"><div class="container"><a href="https://conf.researchr.org/home/<USER>" class="navbar-brand navigate"><span class="brand-text">XP 2025</span></a><div class="place">Mon 2 - Thu 5 June 2025 <a href="https://conf.researchr.org/venue/xp-2025/xp-2025-venue" class="navigate">Brugg - Windisch, Switzerland</a></div></div></div><div class="navbar navbar-default"><div class="container"><div class="navbar-header"><button type="button" data-toggle="collapse" data-target="#navigationbar" class="navbar-toggle"><span class="sr-only">Toggle navigation</span><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></button></div><div id="navigationbar" class="navigationbar collapse navbar-collapse"><ul class="block nav navbar-nav"><li class="dropdown"><a href="Architectural-Evolution.html#" data-toggle="dropdown" class="dropdown-toggle">Attending <span class="caret"></span></a><ul class="block dropdown-menu"><li class="block"><a href="https://conf.researchr.org/venue/xp-2025/xp-2025-venue" class="navigate">Venue: Campus FHNW</a></li><li class="block"><a href="https://conf.researchr.org/attending/xp-2025/travelling-to-brugg" class="navigate">Travelling to Brugg</a></li><li class="block"><a href="https://conf.researchr.org/attending/xp-2025/accommodation" class="navigate">Accommodation</a></li><li class="block"><a href="https://conf.researchr.org/attending/xp-2025/registration" class="navigate">Registration</a></li><li class="block"><a href="https://conf.researchr.org/attending/xp-2025/leisure-activities" class="navigate">Leisure activities in and around Brugg</a></li><li class="block"><a href="https://conf.researchr.org/attending/xp-2025/presenting" class="navigate">Presenting at XP2025</a></li><li class="block"><a href="https://conf.researchr.org/attending/xp-2025/xp2025-code-of-conduct" class="navigate">Code of Conduct</a></li></ul></li><li class="dropdown"><a href="Architectural-Evolution.html#" data-toggle="dropdown" class="dropdown-toggle">Sponsoring <span class="caret"></span></a><ul class="block dropdown-menu"><li class="block"><a href="https://conf.researchr.org/info/xp-2025/our-partners" class="navigate">Our Sponsors and Partners</a></li><li class="block"><a href="https://conf.researchr.org/info/xp-2025/sponsoring" class="navigate">Sponsoring Packages</a></li><li class="block"><a href="https://conf.researchr.org/info/xp-2025/media" class="navigate">Media</a></li></ul></li><li class="dropdown"><a href="Architectural-Evolution.html#" data-toggle="dropdown" class="dropdown-toggle">Program <span class="caret"></span></a><ul id="program-menu" class="block dropdown-menu"><li class="block"><a href="https://conf.researchr.org/program/xp-2025/program-xp-2025/" class="navigate">XP Program</a></li><li class="block"><a href="https://conf.researchr.org/program/xp-2025/program-xp-2025/" data-fltr-type="prog" data-fltr-val="Your Program" class="navigate"><span class="glyphicon glyphicon-star"></span> Your Program</a></li><li class="block divider"></li><li class="block"><a href="https://conf.researchr.org/program/xp-2025/program-xp-2025/" data-fltr-type="date" data-fltr-val="Mon 2 Jun 2025" class="navigate">Mon 2 Jun</a></li><li class="block"><a href="https://conf.researchr.org/program/xp-2025/program-xp-2025/" data-fltr-type="date" data-fltr-val="Tue 3 Jun 2025" class="navigate">Tue 3 Jun</a></li><li class="block"><a href="https://conf.researchr.org/program/xp-2025/program-xp-2025/" data-fltr-type="date" data-fltr-val="Wed 4 Jun 2025" class="navigate">Wed 4 Jun</a></li><li class="block"><a href="https://conf.researchr.org/program/xp-2025/program-xp-2025/" data-fltr-type="date" data-fltr-val="Thu 5 Jun 2025" class="navigate">Thu 5 Jun</a></li></ul></li><li id="tracks-in-navbar" class="dropdown"><a href="Architectural-Evolution.html#" data-toggle="dropdown" class="dropdown-toggle">Tracks <span class="caret"></span></a><ul class="block dropdown-menu multi-column columns-3"><div class="row"><div class="col-sm-5"><ul class="block multi-column-dropdown"><li class="block dropdown-header">XP 2025</li><li class="block"><a href="https://conf.researchr.org/track/xp-2025/xp-2025-agile-training-and-education" class="navigate">Agile Training and Education</a></li><li class="block"><a href="https://conf.researchr.org/track/xp-2025/xp-2025-experience-reports" class="navigate">Experience Reports</a></li><li class="block"><a href="https://conf.researchr.org/track/xp-2025/xp-2025-industry-and-practice" class="navigate">Industry and Practice</a></li><li class="block"><a href="https://conf.researchr.org/track/xp-2025/xp-2025-industry-and-practice-workshops" class="navigate">Industry and Practice Workshops</a></li><li class="block"><a href="https://conf.researchr.org/track/xp-2025/xp-2025-keynotes" class="navigate">Keynotes</a></li><li class="block"><a href="https://conf.researchr.org/track/xp-2025/xp-2025-open-space" class="navigate">Open Space</a></li><li class="block"><a href="https://conf.researchr.org/track/xp-2025/xp-2025-posters" class="navigate">Posters</a></li><li class="block"><a href="https://conf.researchr.org/track/xp-2025/xp-2025-research-papers" class="navigate">Research Papers</a></li><li class="block"><a href="https://conf.researchr.org/track/xp-2025/xp-2025-workshops" class="navigate">Research Workshops</a></li></ul></div><div class="col-sm-7"><ul class="block multi-column-dropdown"><li class="block dropdown-header">Workshops</li><li class="block"><a href="https://conf.researchr.org/home/<USER>/aiandagile-2025" class="navigate"><strong>AIandAgile</strong> AI and Agile Software Development: From Frustration to Success</a></li><li class="block"><a href="https://conf.researchr.org/home/<USER>/agileead-2025" class="navigate"><strong>AgileEAD</strong> Agile Enterprise Architecture and Delivery</a></li><li class="block"><a href="https://conf.researchr.org/home/<USER>/agilepr-2025" class="navigate"><strong>AgilePR</strong> Towards a closer collaboration between practice and research in Agile Software Development – What we learnt; and what not? </a></li><li class="block"><a href="https://conf.researchr.org/home/<USER>/gohyb-2025" class="navigate"><strong>GoHyb</strong> The Third International Workshop on Global and Hybrid Work in Software Engineering (GoHyb)</a></li></ul></div></div></ul></li><li class="dropdown"><a href="Architectural-Evolution.html#" data-toggle="dropdown" class="dropdown-toggle">Organization <span class="caret"></span></a><ul class="block dropdown-menu multi-column columns-2"><div class="row"><div class="col-sm-6"><ul class="block multi-column-dropdown"><li class="block dropdown-header">XP 2025 Committees</li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/xp-2025-organizing-committee" class="navigate">Organizing Committee</a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/xp-2025-steering-committee" class="navigate">Steering Committee</a></li><li class="block dropdown-header">Track Committees  </li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/xp-2025-agile-training-and-education-training-and-education" class="navigate"><strong>Agile Training and Education</strong></a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/xp-2025-experience-reports-program-committee" class="navigate"><strong>Experience Reports</strong></a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/xp-2025-industry-and-practice-programm-committee" class="navigate"><strong>Industry and Practice</strong></a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/xp-2025-keynotes-keynotes" class="navigate"><strong>Keynotes</strong></a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/xp-2025-posters-program-committee" class="navigate"><strong>Posters</strong></a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/xp-2025-reseach-papers-program-committee" class="navigate"><strong>Research Papers</strong></a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/xp-2025-workshops-program-committee" class="navigate"><strong>Research Workshops</strong></a></li><li class="block dropdown-header">Contributors</li><li class="block"><a href="https://conf.researchr.org/people-index/xp-2025" class="navigate"><span class="glyphicon glyphicon-search"></span><sup><span class="glyphicon glyphicon-user"></span></sup> People Index</a></li></ul></div><div class="col-sm-6"><ul class="block multi-column-dropdown"><li class="block dropdown-header">Workshops</li><li class="dropdown dropdown-submenu "><a href="Architectural-Evolution.html#" data-toggle="dropdown" class="dropdown-toggle"><strong>AIandAgile</strong></a><ul class="block dropdown-menu"><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/aiandagile-2025-papers-organizing-committee" class="navigate">Organizing Committee</a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/aiandagile-2025-papers-program-committee" class="navigate">Program Committee</a></li></ul></li><li class="dropdown dropdown-submenu "><a href="Architectural-Evolution.html#" data-toggle="dropdown" class="dropdown-toggle"><strong>AgileEAD</strong></a><ul class="block dropdown-menu"><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/agileead-2025-papers-organizing-committee" class="navigate">Organizing Committee</a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/agileead-2025-papers-program-committee" class="navigate">Program Committee</a></li></ul></li><li class="dropdown dropdown-submenu "><a href="Architectural-Evolution.html#" data-toggle="dropdown" class="dropdown-toggle"><strong>AgilePR</strong></a><ul class="block dropdown-menu"><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/agilepr-2025-papers-organizing-committee" class="navigate">Organizing Committee</a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/agilepr-2025-papers-program-committee" class="navigate">Program Committee</a></li></ul></li><li class="dropdown dropdown-submenu "><a href="Architectural-Evolution.html#" data-toggle="dropdown" class="dropdown-toggle"><strong>GoHyb</strong></a><ul class="block dropdown-menu"><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/gohyb-2025-papers-organizing-committee" class="navigate">Organizing Committee</a></li><li class="block"><a href="https://conf.researchr.org/committee/xp-2025/gohyb-2025-papers-program-committee" class="navigate">Program Committee</a></li></ul></li></ul></div></div></ul></li><li class="block"><a href="https://conf.researchr.org/search/xp-2025//all" class="navigate"><span class="glyphicon glyphicon-search"></span><span class="hidden-sm"> Search</span></a></li><li class="block"><li class="block"><a href="https://conf.researchr.org/series/xp" class="navigate"><span class="hidden-sm">Series</span><span title="Series" class="visible-sm"><span class="glyphicon glyphicon-repeat"></span></span></a></li></li></ul><div class="navbar-right"><ul class="block nav navbar-nav"><li class="block"><a href="https://conf.researchr.org/signin/xp-2025/https%3A%5Es%5Esconf.researchr.org%5Esdetails%5Esxp-2025%5Esagileead-2025-papers%5Es8%5EsArchitectural-Evolution" rel="nofollow" class="navigate">Sign in</a></li><li class="block"><a href="https://conf.researchr.org/signup/xp-2025" class="navigate">Sign up</a></li></ul></div></div></div></div><div id="content" class="container"><script type="text/javascript">if(gtag) { gtag( 'event', 'Show Event Page' , {'event_category': 'Event Information', 'event_label': 'Architectural Evolution'} ) }</script><div class="page-header"><span class="text-muted small"><span class="glyphicon glyphicon-home"></span> <a href="https://conf.researchr.org/home/<USER>" class="navigate">XP 2025</a> (<a href="https://conf.researchr.org/series/xp" class="navigate">series</a>) / </span><span class="text-muted small">AgileEAD 2025 (<a href="https://conf.researchr.org/series/agileead" class="navigate">series</a>) / </span><span class="text-muted small"><span class="glyphicon glyphicon-road"></span> <a href="https://conf.researchr.org/home/<USER>/agileead-2025" class="navigate">Agile Enterprise Architecture and Delivery</a> / </span><h2>Architectural Evolution</h2></div><div class="row"><div class="col-sm-8 col-lg-9"><div class="row" style="padding:4px 0px;"><label class="col-sm-2 control-label" style="text-align:right;">Track</label><div class="col-sm-10"><a href="https://conf.researchr.org/home/<USER>/agileead-2025" class="navigate">AgileEAD 2025 Agile Enterprise Architecture and Delivery</a></div></div><script type="text/javascript">(function(){
    var post_process_function = function(n){ var node=(n&&n.nodeType === 1)?n:document; $(node).find('.modal').on('shown.bs.modal', function() {if(gtag) { gtag( 'event', 'Show Details in Modal' , {'event_category': 'Event Information', 'event_label': '' + $(this).find('.modal-title').text() + ''} ) } } ) };
    var original_post_process_func = ajax_post_process;
    ajax_post_process = function(){
      original_post_process_func.apply(this,arguments);
      post_process_function.apply(this,arguments);
    };
    $(document).ready( post_process_function );
  })();   
  </script><div tabindex="-1" id="hidden-modal" class="modal"><div class="modal-dialog "><div class="modal-content"></div></div></div><div id="program-settings-modal" class="webdsl-placeholder"><div id="userProgramSettings" class="modal fade"><div class="modal-dialog "><div class="modal-content"><div class="modal-header"><a data-dismiss="modal" class="close"><span class="glyphicon glyphicon-remove"></span></a><h3>Program Display Configuration</h3></div><div class="modal-body"><form name="form11390898770538530f0e2bbdb67872b491120c5c8a7" id="form11390898770538530f0e2bbdb67872b491120c5c8a7" action="https://conf.researchr.org/userProgramSettingsModalConferenceEdition" accept-charset="UTF-8" method="POST" role="form" class="form-horizontal"><input type="hidden" name="form11390898770538530f0e2bbdb67872b491120c5c8a7" value="1" /><input type="hidden" name="context" value="xp-2025" /><div class="panel panel-default timezone-panel"><div class="panel-heading clearfix"><div class="panel-title">Time Zone</div></div><div class="panel-body"><span class="help-block">The program is currently displayed in <strong>(GMT+02:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna</strong>.</span><div class="row"><div class="col-sm-11 col-sm-offset-1"><label class="radio"><input type="radio" checked="checked" name="9659afaa126198132be460b106e35ccb" value="false" onchange="if( ($(this).val() == 'true' &amp;&amp; this.checked) != $('#cec04de0447905c49fb377c6d9693505').is(':visible') ) { $('#cec04de0447905c49fb377c6d9693505').fadeToggle( 250 ); }"/><strong>Use conference time zone: (GMT+02:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna</strong></label><label class="radio"><input type="radio" name="9659afaa126198132be460b106e35ccb" value="true" onchange="if( ($(this).val() == 'true' &amp;&amp; this.checked) != $('#cec04de0447905c49fb377c6d9693505').is(':visible') ) { $('#cec04de0447905c49fb377c6d9693505').fadeToggle( 250 ); }"/>Select other time zone</label><div id="cec04de0447905c49fb377c6d9693505" style=" display: none;"><select name="0e35d6393a8ba494aa0723dd51b6899c" class="select form-control"><option value="" selected="selected" data-utc-minute-offset="0"></option><option value="Etc/GMT+12" data-utc-minute-offset="-720">(GMT-12:00) AoE (Anywhere On Earth)</option><option value="Pacific/Midway" data-utc-minute-offset="-660">(GMT-11:00) Midway Island, Samoa</option><option value="America/Adak" data-utc-minute-offset="-540">(GMT-09:00) Hawaii-Aleutian</option><option value="Etc/GMT+10" data-utc-minute-offset="-600">(GMT-10:00) Hawaii</option><option value="Pacific/Marquesas" data-utc-minute-offset="-570">(GMT-09:30) Marquesas Islands</option><option value="Pacific/Gambier" data-utc-minute-offset="-540">(GMT-09:00) Gambier Islands</option><option value="America/Anchorage" data-utc-minute-offset="-480">(GMT-08:00) Alaska</option><option value="America/Ensenada" data-utc-minute-offset="-420">(GMT-07:00) Tijuana, Baja California</option><option value="Etc/GMT+8" data-utc-minute-offset="-480">(GMT-08:00) Pitcairn Islands</option><option value="America/Los_Angeles" data-utc-minute-offset="-420">(GMT-07:00) Pacific Time (US &amp; Canada)</option><option value="America/Denver" data-utc-minute-offset="-360">(GMT-06:00) Mountain Time (US &amp; Canada)</option><option value="America/Chihuahua" data-utc-minute-offset="-360">(GMT-06:00) Chihuahua, La Paz, Mazatlan</option><option value="America/Dawson_Creek" data-utc-minute-offset="-420">(GMT-07:00) Arizona</option><option value="America/Belize" data-utc-minute-offset="-360">(GMT-06:00) Saskatchewan, Central America</option><option value="America/Cancun" data-utc-minute-offset="-300">(GMT-05:00) Guadalajara, Mexico City, Monterrey</option><option value="Chile/EasterIsland" data-utc-minute-offset="-360">(GMT-06:00) Easter Island</option><option value="America/Chicago" data-utc-minute-offset="-300">(GMT-05:00) Central Time (US &amp; Canada)</option><option value="America/New_York" data-utc-minute-offset="-240">(GMT-04:00) Eastern Time (US &amp; Canada)</option><option value="America/Havana" data-utc-minute-offset="-240">(GMT-04:00) Cuba</option><option value="America/Bogota" data-utc-minute-offset="-300">(GMT-05:00) Bogota, Lima, Quito, Rio Branco</option><option value="America/Caracas" data-utc-minute-offset="-240">(GMT-04:00) Caracas</option><option value="America/Santiago" data-utc-minute-offset="-240">(GMT-04:00) Santiago</option><option value="America/La_Paz" data-utc-minute-offset="-240">(GMT-04:00) La Paz</option><option value="Atlantic/Stanley" data-utc-minute-offset="-180">(GMT-03:00) Faukland Islands</option><option value="America/Campo_Grande" data-utc-minute-offset="-240">(GMT-04:00) Manaus, Amazonas, Brazil</option><option value="America/Goose_Bay" data-utc-minute-offset="-180">(GMT-03:00) Atlantic Time (Goose Bay)</option><option value="America/Glace_Bay" data-utc-minute-offset="-180">(GMT-03:00) Atlantic Time (Canada)</option><option value="America/St_Johns" data-utc-minute-offset="-150">(GMT-02:30) Newfoundland</option><option value="America/Araguaina" data-utc-minute-offset="-180">(GMT-03:00) UTC-3</option><option value="America/Montevideo" data-utc-minute-offset="-180">(GMT-03:00) Montevideo</option><option value="America/Miquelon" data-utc-minute-offset="-120">(GMT-02:00) Miquelon, St. Pierre</option><option value="America/Godthab" data-utc-minute-offset="-120">(GMT-02:00) Greenland</option><option value="America/Argentina/Buenos_Aires" data-utc-minute-offset="-180">(GMT-03:00) Buenos Aires</option><option value="America/Sao_Paulo" data-utc-minute-offset="-180">(GMT-03:00) Brasilia, Distrito Federal, Brazil</option><option value="America/Noronha" data-utc-minute-offset="-120">(GMT-02:00) Mid-Atlantic</option><option value="Atlantic/Cape_Verde" data-utc-minute-offset="-60">(GMT-01:00) Cape Verde Is.</option><option value="Atlantic/Azores" data-utc-minute-offset="0">(GMT) Azores</option><option value="Etc/UTC" data-utc-minute-offset="0">(UTC) Coordinated Universal Time</option><option value="Europe/Belfast" data-utc-minute-offset="60">(GMT+01:00) Belfast</option><option value="Europe/Dublin" data-utc-minute-offset="60">(GMT+01:00) Dublin</option><option value="Europe/Lisbon" data-utc-minute-offset="60">(GMT+01:00) Lisbon</option><option value="Europe/London" data-utc-minute-offset="60">(GMT+01:00) London</option><option value="Africa/Abidjan" data-utc-minute-offset="0">(GMT) Monrovia, Reykjavik</option><option value="Europe/Amsterdam" data-utc-minute-offset="120">(GMT+02:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna</option><option value="Europe/Belgrade" data-utc-minute-offset="120">(GMT+02:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague</option><option value="Europe/Brussels" data-utc-minute-offset="120">(GMT+02:00) Brussels, Copenhagen, Madrid, Paris</option><option value="Africa/Algiers" data-utc-minute-offset="60">(GMT+01:00) West Central Africa</option><option value="Africa/Windhoek" data-utc-minute-offset="120">(GMT+02:00) Windhoek</option><option value="Europe/Athens" data-utc-minute-offset="180">(GMT+03:00) Athens</option><option value="Asia/Beirut" data-utc-minute-offset="180">(GMT+03:00) Beirut</option><option value="Africa/Cairo" data-utc-minute-offset="120">(GMT+02:00) Cairo</option><option value="Asia/Gaza" data-utc-minute-offset="180">(GMT+03:00) Gaza</option><option value="Africa/Blantyre" data-utc-minute-offset="120">(GMT+02:00) Harare, Pretoria</option><option value="Asia/Jerusalem" data-utc-minute-offset="180">(GMT+03:00) Jerusalem</option><option value="Europe/Minsk" data-utc-minute-offset="180">(GMT+03:00) Minsk</option><option value="Asia/Damascus" data-utc-minute-offset="180">(GMT+03:00) Syria</option><option value="Europe/Moscow" data-utc-minute-offset="180">(GMT+03:00) Moscow, St. Petersburg, Volgograd</option><option value="Africa/Addis_Ababa" data-utc-minute-offset="180">(GMT+03:00) Nairobi</option><option value="Asia/Tehran" data-utc-minute-offset="210">(GMT+03:30) Tehran</option><option value="Asia/Dubai" data-utc-minute-offset="240">(GMT+04:00) Abu Dhabi, Muscat</option><option value="Asia/Yerevan" data-utc-minute-offset="240">(GMT+04:00) Yerevan</option><option value="Asia/Kabul" data-utc-minute-offset="270">(GMT+04:30) Kabul</option><option value="Asia/Yekaterinburg" data-utc-minute-offset="300">(GMT+05:00) Ekaterinburg</option><option value="Asia/Tashkent" data-utc-minute-offset="300">(GMT+05:00) Tashkent</option><option value="Asia/Kolkata" data-utc-minute-offset="330">(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi</option><option value="Asia/Katmandu" data-utc-minute-offset="345">(GMT+05:45) Kathmandu</option><option value="Asia/Dhaka" data-utc-minute-offset="360">(GMT+06:00) Astana, Dhaka</option><option value="Asia/Novosibirsk" data-utc-minute-offset="420">(GMT+07:00) Novosibirsk</option><option value="Asia/Rangoon" data-utc-minute-offset="390">(GMT+06:30) Yangon (Rangoon)</option><option value="Asia/Bangkok" data-utc-minute-offset="420">(GMT+07:00) Bangkok, Hanoi, Jakarta</option><option value="Asia/Krasnoyarsk" data-utc-minute-offset="420">(GMT+07:00) Krasnoyarsk</option><option value="Asia/Hong_Kong" data-utc-minute-offset="480">(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi</option><option value="Asia/Irkutsk" data-utc-minute-offset="480">(GMT+08:00) Irkutsk, Ulaan Bataar</option><option value="Australia/Perth" data-utc-minute-offset="480">(GMT+08:00) Perth</option><option value="Australia/Eucla" data-utc-minute-offset="525">(GMT+08:45) Eucla</option><option value="Asia/Tokyo" data-utc-minute-offset="540">(GMT+09:00) Osaka, Sapporo, Tokyo</option><option value="Asia/Seoul" data-utc-minute-offset="540">(GMT+09:00) Seoul</option><option value="Asia/Yakutsk" data-utc-minute-offset="540">(GMT+09:00) Yakutsk</option><option value="Australia/Adelaide" data-utc-minute-offset="570">(GMT+09:30) Adelaide</option><option value="Australia/Darwin" data-utc-minute-offset="570">(GMT+09:30) Darwin</option><option value="Australia/Brisbane" data-utc-minute-offset="600">(GMT+10:00) Brisbane</option><option value="Australia/Hobart" data-utc-minute-offset="600">(GMT+10:00) Hobart</option><option value="Asia/Vladivostok" data-utc-minute-offset="600">(GMT+10:00) Vladivostok</option><option value="Australia/Lord_Howe" data-utc-minute-offset="630">(GMT+10:30) Lord Howe Island</option><option value="Etc/GMT-11" data-utc-minute-offset="660">(GMT+11:00) Solomon Is., New Caledonia</option><option value="Asia/Magadan" data-utc-minute-offset="660">(GMT+11:00) Magadan</option><option value="Pacific/Norfolk" data-utc-minute-offset="660">(GMT+11:00) Norfolk Island</option><option value="Asia/Anadyr" data-utc-minute-offset="720">(GMT+12:00) Anadyr, Kamchatka</option><option value="Pacific/Auckland" data-utc-minute-offset="720">(GMT+12:00) Auckland, Wellington</option><option value="Etc/GMT-12" data-utc-minute-offset="720">(GMT+12:00) Fiji, Kamchatka, Marshall Is.</option><option value="Pacific/Chatham" data-utc-minute-offset="765">(GMT+12:45) Chatham Islands</option><option value="Pacific/Tongatapu" data-utc-minute-offset="780">(GMT+13:00) Nuku'alofa</option><option value="Pacific/Kiritimati" data-utc-minute-offset="840">(GMT+14:00) Kiritimati</option></select><script type="text/javascript">var tzOffsetMin = new Date().getTimezoneOffset()*-1;
			var sel = document.getElementsByName('0e35d6393a8ba494aa0723dd51b6899c')[0]
			var opts = sel.options;
			for (var opt, j = 0; opt = opts[j]; j++) {
			  if (opt.getAttribute('data-utc-minute-offset') == tzOffsetMin) {
			    sel.selectedIndex = j;
			    break;
			  }
			}
  	</script></div></div></div><br><span class="help-block"><small><span class="glyphicon glyphicon-info-sign"></span> The GMT offsets shown reflect the offsets <strong>at the moment of the conference</strong>.</small></span></div></div><div class="panel panel-default timeband-panel"><div class="panel-heading clearfix"><div class="panel-title">Time Band</div></div><div class="panel-body"><span class="help-block">By setting a time band, the program will dim events that are outside this time window. This is useful for (virtual) conferences with a continuous program (with repeated sessions).<br>The time band will also limit the events that are included in the personal iCalendar subscription service.</span><div class="row"><div class="col-sm-11 col-sm-offset-1"><label class="radio"><input type="radio" checked="checked" name="f1869fcfb40f29e9c9c46042f4fbda2c" value="false" onchange="if( ($(this).val() == 'true' &amp;&amp; this.checked) != $('#c2371058bcb9b31dd10e115e7f712708').is(':visible') ) { $('#c2371058bcb9b31dd10e115e7f712708').fadeToggle( 250 ); }"/><strong>Display full program</strong></label><label class="radio"><input type="radio" name="f1869fcfb40f29e9c9c46042f4fbda2c" value="true" onchange="if( ($(this).val() == 'true' &amp;&amp; this.checked) != $('#c2371058bcb9b31dd10e115e7f712708').is(':visible') ) { $('#c2371058bcb9b31dd10e115e7f712708').fadeToggle( 250 ); }"/>Specify a time band</label><div id="c2371058bcb9b31dd10e115e7f712708" style=" display: none;"><span class="input-group"><input name="a4e58ac1cd9cfb26fb62ecc29e600148" type="text" value="07:00" class="flatpickr inputDate form-control"/><script type="text/javascript">$("input:not(.flatpickr-input)[name=a4e58ac1cd9cfb26fb62ecc29e600148]").flatpickr({onOpen: function(dateObj, dateStr, instance){ if(dateStr == ''){ instance.jumpToDate( new Date() ); } }, allowInput: true, dateformat: 'H:i', altFormat: 'H:i' , altInput: true, time_24hr: true, noCalendar: true, enableTime : true});
  </script><span class="input-group-addon"> - </span><input name="01ff55ee23f3ce1eb7d922f922eb09b1" type="text" value="19:00" class="flatpickr inputDate form-control"/><script type="text/javascript">$("input:not(.flatpickr-input)[name=01ff55ee23f3ce1eb7d922f922eb09b1]").flatpickr({onOpen: function(dateObj, dateStr, instance){ if(dateStr == ''){ instance.jumpToDate( new Date() ); } }, allowInput: true, dateformat: 'H:i', altFormat: 'H:i' , altInput: true, time_24hr: true, noCalendar: true, enableTime : true});
  </script></span></div></div></div></div></div><div class="row form-group"><div class="col-sm-offset-2 col-sm-10"><button style="position: absolute; left: -9999px; width: 1px; height: 1px;" onclick='javascript:serverInvoke("https://conf.researchr.org/userProgramSettingsModalConferenceEdition","userProgramSettingsModalConferenceEdition_apply18ec3dfeeecb5c44c1e40a40983898869", [{"name":"context", "value":"xp-2025"},],"form11390898770538530f0e2bbdb67872b491120c5c8a7", this.nextSibling, true,"program-settings-modal"); return false;'></button><a submitid="userProgramSettingsModalConferenceEdition_apply18ec3dfeeecb5c44c1e40a40983898869" href="javascript:void(0)" onclick="javascript:loadImageElem=this;$(this.previousSibling).click()" class="btn btn-primary btn btn-default">Save</a></div></div></form></div><div class="modal-footer"><a href="Architectural-Evolution.html#" data-dismiss="modal" class="btn btn-default"><span class="glyphicon glyphicon-remove"></span> Close</a></div></div></div></div></div><div class="program-container"><div class="row" style="padding:4px 0px;"><label class="col-sm-2 control-label" style="text-align:right;">When</label><div class="col-sm-10"><strong>Mon 2 Jun 2025 14:45 - 15:30 at <a href="https://conf.researchr.org/room/xp-2025/xp-2025-venue-6.0d09-%28workshop%29" class="room-link navigate">6.0D09 (Workshop)</a></strong> - <a href="https://conf.researchr.org/home/<USER>/agileead-2025#program" class="navigate">AgileEAD</a></div></div></div><div class="row" style="padding:4px 0px;"><label class="col-sm-2 control-label" style="text-align:right;">File attachments</label><div class="col-sm-10"><table class="table table-bordered table-striped table-condensed"><tr><td>Slides (<a href="javascript:void(0)" onclick='javascript:serverInvokeDownloadCompatible("https://conf.researchr.org/details","335a08262ad742de9568856d3a051982action_17426506610eae717cc65b93c14b7d80b7aa442aa34", [{"name":"context", "value":"xp-2025"},{"name":"track", "value":"agileead-2025-papers"},{"name":"urlKey", "value":"8"},{"name":"decoTitle", "value":"Architectural-Evolution"},],"", this,"1")' class="downloadlink">The Jedis Path to a Software Architecture that Evolves - simplified version.pdf</a>)</td><td>5.0MiB</td></tr></table></div></div><script type="text/javascript">(function(){
    var post_process_function = function(n){ var node=(n&&n.nodeType === 1)?n:document; $(node).find('.modal').on('shown.bs.modal', function() {if(gtag) { gtag( 'event', 'Show Details in Modal' , {'event_category': 'Event Information', 'event_label': '' + $(this).find('.modal-title').text() + ''} ) } } ) };
    var original_post_process_func = ajax_post_process;
    ajax_post_process = function(){
      original_post_process_func.apply(this,arguments);
      post_process_function.apply(this,arguments);
    };
    $(document).ready( post_process_function );
  })();   
  </script><div tabindex="-1" id="hidden-modal" class="modal"><div class="modal-dialog "><div class="modal-content"></div></div></div><div id="program-settings-modal" class="webdsl-placeholder"><div id="userProgramSettings" class="modal fade"><div class="modal-dialog "><div class="modal-content"><div class="modal-header"><a data-dismiss="modal" class="close"><span class="glyphicon glyphicon-remove"></span></a><h3>Program Display Configuration</h3></div><div class="modal-body"><form name="form11390898770538530f0e2bbdb67872b491120c5c8a7" id="form11390898770538530f0e2bbdb67872b491120c5c8a7" action="https://conf.researchr.org/userProgramSettingsModalConferenceEdition" accept-charset="UTF-8" method="POST" role="form" class="form-horizontal"><input type="hidden" name="form11390898770538530f0e2bbdb67872b491120c5c8a7" value="1" /><input type="hidden" name="context" value="xp-2025" /><div class="panel panel-default timezone-panel"><div class="panel-heading clearfix"><div class="panel-title">Time Zone</div></div><div class="panel-body"><span class="help-block">The program is currently displayed in <strong>(GMT+02:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna</strong>.</span><div class="row"><div class="col-sm-11 col-sm-offset-1"><label class="radio"><input type="radio" checked="checked" name="9659afaa126198132be460b106e35ccb" value="false" onchange="if( ($(this).val() == 'true' &amp;&amp; this.checked) != $('#cec04de0447905c49fb377c6d9693505').is(':visible') ) { $('#cec04de0447905c49fb377c6d9693505').fadeToggle( 250 ); }"/><strong>Use conference time zone: (GMT+02:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna</strong></label><label class="radio"><input type="radio" name="9659afaa126198132be460b106e35ccb" value="true" onchange="if( ($(this).val() == 'true' &amp;&amp; this.checked) != $('#cec04de0447905c49fb377c6d9693505').is(':visible') ) { $('#cec04de0447905c49fb377c6d9693505').fadeToggle( 250 ); }"/>Select other time zone</label><div id="cec04de0447905c49fb377c6d9693505" style=" display: none;"><select name="0e35d6393a8ba494aa0723dd51b6899c" class="select form-control"><option value="" selected="selected" data-utc-minute-offset="0"></option><option value="Etc/GMT+12" data-utc-minute-offset="-720">(GMT-12:00) AoE (Anywhere On Earth)</option><option value="Pacific/Midway" data-utc-minute-offset="-660">(GMT-11:00) Midway Island, Samoa</option><option value="America/Adak" data-utc-minute-offset="-540">(GMT-09:00) Hawaii-Aleutian</option><option value="Etc/GMT+10" data-utc-minute-offset="-600">(GMT-10:00) Hawaii</option><option value="Pacific/Marquesas" data-utc-minute-offset="-570">(GMT-09:30) Marquesas Islands</option><option value="Pacific/Gambier" data-utc-minute-offset="-540">(GMT-09:00) Gambier Islands</option><option value="America/Anchorage" data-utc-minute-offset="-480">(GMT-08:00) Alaska</option><option value="America/Ensenada" data-utc-minute-offset="-420">(GMT-07:00) Tijuana, Baja California</option><option value="Etc/GMT+8" data-utc-minute-offset="-480">(GMT-08:00) Pitcairn Islands</option><option value="America/Los_Angeles" data-utc-minute-offset="-420">(GMT-07:00) Pacific Time (US &amp; Canada)</option><option value="America/Denver" data-utc-minute-offset="-360">(GMT-06:00) Mountain Time (US &amp; Canada)</option><option value="America/Chihuahua" data-utc-minute-offset="-360">(GMT-06:00) Chihuahua, La Paz, Mazatlan</option><option value="America/Dawson_Creek" data-utc-minute-offset="-420">(GMT-07:00) Arizona</option><option value="America/Belize" data-utc-minute-offset="-360">(GMT-06:00) Saskatchewan, Central America</option><option value="America/Cancun" data-utc-minute-offset="-300">(GMT-05:00) Guadalajara, Mexico City, Monterrey</option><option value="Chile/EasterIsland" data-utc-minute-offset="-360">(GMT-06:00) Easter Island</option><option value="America/Chicago" data-utc-minute-offset="-300">(GMT-05:00) Central Time (US &amp; Canada)</option><option value="America/New_York" data-utc-minute-offset="-240">(GMT-04:00) Eastern Time (US &amp; Canada)</option><option value="America/Havana" data-utc-minute-offset="-240">(GMT-04:00) Cuba</option><option value="America/Bogota" data-utc-minute-offset="-300">(GMT-05:00) Bogota, Lima, Quito, Rio Branco</option><option value="America/Caracas" data-utc-minute-offset="-240">(GMT-04:00) Caracas</option><option value="America/Santiago" data-utc-minute-offset="-240">(GMT-04:00) Santiago</option><option value="America/La_Paz" data-utc-minute-offset="-240">(GMT-04:00) La Paz</option><option value="Atlantic/Stanley" data-utc-minute-offset="-180">(GMT-03:00) Faukland Islands</option><option value="America/Campo_Grande" data-utc-minute-offset="-240">(GMT-04:00) Manaus, Amazonas, Brazil</option><option value="America/Goose_Bay" data-utc-minute-offset="-180">(GMT-03:00) Atlantic Time (Goose Bay)</option><option value="America/Glace_Bay" data-utc-minute-offset="-180">(GMT-03:00) Atlantic Time (Canada)</option><option value="America/St_Johns" data-utc-minute-offset="-150">(GMT-02:30) Newfoundland</option><option value="America/Araguaina" data-utc-minute-offset="-180">(GMT-03:00) UTC-3</option><option value="America/Montevideo" data-utc-minute-offset="-180">(GMT-03:00) Montevideo</option><option value="America/Miquelon" data-utc-minute-offset="-120">(GMT-02:00) Miquelon, St. Pierre</option><option value="America/Godthab" data-utc-minute-offset="-120">(GMT-02:00) Greenland</option><option value="America/Argentina/Buenos_Aires" data-utc-minute-offset="-180">(GMT-03:00) Buenos Aires</option><option value="America/Sao_Paulo" data-utc-minute-offset="-180">(GMT-03:00) Brasilia, Distrito Federal, Brazil</option><option value="America/Noronha" data-utc-minute-offset="-120">(GMT-02:00) Mid-Atlantic</option><option value="Atlantic/Cape_Verde" data-utc-minute-offset="-60">(GMT-01:00) Cape Verde Is.</option><option value="Atlantic/Azores" data-utc-minute-offset="0">(GMT) Azores</option><option value="Etc/UTC" data-utc-minute-offset="0">(UTC) Coordinated Universal Time</option><option value="Europe/Belfast" data-utc-minute-offset="60">(GMT+01:00) Belfast</option><option value="Europe/Dublin" data-utc-minute-offset="60">(GMT+01:00) Dublin</option><option value="Europe/Lisbon" data-utc-minute-offset="60">(GMT+01:00) Lisbon</option><option value="Europe/London" data-utc-minute-offset="60">(GMT+01:00) London</option><option value="Africa/Abidjan" data-utc-minute-offset="0">(GMT) Monrovia, Reykjavik</option><option value="Europe/Amsterdam" data-utc-minute-offset="120">(GMT+02:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna</option><option value="Europe/Belgrade" data-utc-minute-offset="120">(GMT+02:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague</option><option value="Europe/Brussels" data-utc-minute-offset="120">(GMT+02:00) Brussels, Copenhagen, Madrid, Paris</option><option value="Africa/Algiers" data-utc-minute-offset="60">(GMT+01:00) West Central Africa</option><option value="Africa/Windhoek" data-utc-minute-offset="120">(GMT+02:00) Windhoek</option><option value="Europe/Athens" data-utc-minute-offset="180">(GMT+03:00) Athens</option><option value="Asia/Beirut" data-utc-minute-offset="180">(GMT+03:00) Beirut</option><option value="Africa/Cairo" data-utc-minute-offset="120">(GMT+02:00) Cairo</option><option value="Asia/Gaza" data-utc-minute-offset="180">(GMT+03:00) Gaza</option><option value="Africa/Blantyre" data-utc-minute-offset="120">(GMT+02:00) Harare, Pretoria</option><option value="Asia/Jerusalem" data-utc-minute-offset="180">(GMT+03:00) Jerusalem</option><option value="Europe/Minsk" data-utc-minute-offset="180">(GMT+03:00) Minsk</option><option value="Asia/Damascus" data-utc-minute-offset="180">(GMT+03:00) Syria</option><option value="Europe/Moscow" data-utc-minute-offset="180">(GMT+03:00) Moscow, St. Petersburg, Volgograd</option><option value="Africa/Addis_Ababa" data-utc-minute-offset="180">(GMT+03:00) Nairobi</option><option value="Asia/Tehran" data-utc-minute-offset="210">(GMT+03:30) Tehran</option><option value="Asia/Dubai" data-utc-minute-offset="240">(GMT+04:00) Abu Dhabi, Muscat</option><option value="Asia/Yerevan" data-utc-minute-offset="240">(GMT+04:00) Yerevan</option><option value="Asia/Kabul" data-utc-minute-offset="270">(GMT+04:30) Kabul</option><option value="Asia/Yekaterinburg" data-utc-minute-offset="300">(GMT+05:00) Ekaterinburg</option><option value="Asia/Tashkent" data-utc-minute-offset="300">(GMT+05:00) Tashkent</option><option value="Asia/Kolkata" data-utc-minute-offset="330">(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi</option><option value="Asia/Katmandu" data-utc-minute-offset="345">(GMT+05:45) Kathmandu</option><option value="Asia/Dhaka" data-utc-minute-offset="360">(GMT+06:00) Astana, Dhaka</option><option value="Asia/Novosibirsk" data-utc-minute-offset="420">(GMT+07:00) Novosibirsk</option><option value="Asia/Rangoon" data-utc-minute-offset="390">(GMT+06:30) Yangon (Rangoon)</option><option value="Asia/Bangkok" data-utc-minute-offset="420">(GMT+07:00) Bangkok, Hanoi, Jakarta</option><option value="Asia/Krasnoyarsk" data-utc-minute-offset="420">(GMT+07:00) Krasnoyarsk</option><option value="Asia/Hong_Kong" data-utc-minute-offset="480">(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi</option><option value="Asia/Irkutsk" data-utc-minute-offset="480">(GMT+08:00) Irkutsk, Ulaan Bataar</option><option value="Australia/Perth" data-utc-minute-offset="480">(GMT+08:00) Perth</option><option value="Australia/Eucla" data-utc-minute-offset="525">(GMT+08:45) Eucla</option><option value="Asia/Tokyo" data-utc-minute-offset="540">(GMT+09:00) Osaka, Sapporo, Tokyo</option><option value="Asia/Seoul" data-utc-minute-offset="540">(GMT+09:00) Seoul</option><option value="Asia/Yakutsk" data-utc-minute-offset="540">(GMT+09:00) Yakutsk</option><option value="Australia/Adelaide" data-utc-minute-offset="570">(GMT+09:30) Adelaide</option><option value="Australia/Darwin" data-utc-minute-offset="570">(GMT+09:30) Darwin</option><option value="Australia/Brisbane" data-utc-minute-offset="600">(GMT+10:00) Brisbane</option><option value="Australia/Hobart" data-utc-minute-offset="600">(GMT+10:00) Hobart</option><option value="Asia/Vladivostok" data-utc-minute-offset="600">(GMT+10:00) Vladivostok</option><option value="Australia/Lord_Howe" data-utc-minute-offset="630">(GMT+10:30) Lord Howe Island</option><option value="Etc/GMT-11" data-utc-minute-offset="660">(GMT+11:00) Solomon Is., New Caledonia</option><option value="Asia/Magadan" data-utc-minute-offset="660">(GMT+11:00) Magadan</option><option value="Pacific/Norfolk" data-utc-minute-offset="660">(GMT+11:00) Norfolk Island</option><option value="Asia/Anadyr" data-utc-minute-offset="720">(GMT+12:00) Anadyr, Kamchatka</option><option value="Pacific/Auckland" data-utc-minute-offset="720">(GMT+12:00) Auckland, Wellington</option><option value="Etc/GMT-12" data-utc-minute-offset="720">(GMT+12:00) Fiji, Kamchatka, Marshall Is.</option><option value="Pacific/Chatham" data-utc-minute-offset="765">(GMT+12:45) Chatham Islands</option><option value="Pacific/Tongatapu" data-utc-minute-offset="780">(GMT+13:00) Nuku'alofa</option><option value="Pacific/Kiritimati" data-utc-minute-offset="840">(GMT+14:00) Kiritimati</option></select><script type="text/javascript">var tzOffsetMin = new Date().getTimezoneOffset()*-1;
			var sel = document.getElementsByName('0e35d6393a8ba494aa0723dd51b6899c')[0]
			var opts = sel.options;
			for (var opt, j = 0; opt = opts[j]; j++) {
			  if (opt.getAttribute('data-utc-minute-offset') == tzOffsetMin) {
			    sel.selectedIndex = j;
			    break;
			  }
			}
  	</script></div></div></div><br><span class="help-block"><small><span class="glyphicon glyphicon-info-sign"></span> The GMT offsets shown reflect the offsets <strong>at the moment of the conference</strong>.</small></span></div></div><div class="panel panel-default timeband-panel"><div class="panel-heading clearfix"><div class="panel-title">Time Band</div></div><div class="panel-body"><span class="help-block">By setting a time band, the program will dim events that are outside this time window. This is useful for (virtual) conferences with a continuous program (with repeated sessions).<br>The time band will also limit the events that are included in the personal iCalendar subscription service.</span><div class="row"><div class="col-sm-11 col-sm-offset-1"><label class="radio"><input type="radio" checked="checked" name="f1869fcfb40f29e9c9c46042f4fbda2c" value="false" onchange="if( ($(this).val() == 'true' &amp;&amp; this.checked) != $('#c2371058bcb9b31dd10e115e7f712708').is(':visible') ) { $('#c2371058bcb9b31dd10e115e7f712708').fadeToggle( 250 ); }"/><strong>Display full program</strong></label><label class="radio"><input type="radio" name="f1869fcfb40f29e9c9c46042f4fbda2c" value="true" onchange="if( ($(this).val() == 'true' &amp;&amp; this.checked) != $('#c2371058bcb9b31dd10e115e7f712708').is(':visible') ) { $('#c2371058bcb9b31dd10e115e7f712708').fadeToggle( 250 ); }"/>Specify a time band</label><div id="c2371058bcb9b31dd10e115e7f712708" style=" display: none;"><span class="input-group"><input name="a4e58ac1cd9cfb26fb62ecc29e600148" type="text" value="07:00" class="flatpickr inputDate form-control"/><script type="text/javascript">$("input:not(.flatpickr-input)[name=a4e58ac1cd9cfb26fb62ecc29e600148]").flatpickr({onOpen: function(dateObj, dateStr, instance){ if(dateStr == ''){ instance.jumpToDate( new Date() ); } }, allowInput: true, dateformat: 'H:i', altFormat: 'H:i' , altInput: true, time_24hr: true, noCalendar: true, enableTime : true});
  </script><span class="input-group-addon"> - </span><input name="01ff55ee23f3ce1eb7d922f922eb09b1" type="text" value="19:00" class="flatpickr inputDate form-control"/><script type="text/javascript">$("input:not(.flatpickr-input)[name=01ff55ee23f3ce1eb7d922f922eb09b1]").flatpickr({onOpen: function(dateObj, dateStr, instance){ if(dateStr == ''){ instance.jumpToDate( new Date() ); } }, allowInput: true, dateformat: 'H:i', altFormat: 'H:i' , altInput: true, time_24hr: true, noCalendar: true, enableTime : true});
  </script></span></div></div></div></div></div><div class="row form-group"><div class="col-sm-offset-2 col-sm-10"><button style="position: absolute; left: -9999px; width: 1px; height: 1px;" onclick='javascript:serverInvoke("https://conf.researchr.org/userProgramSettingsModalConferenceEdition","userProgramSettingsModalConferenceEdition_apply18ec3dfeeecb5c44c1e40a40983898869", [{"name":"context", "value":"xp-2025"},],"form11390898770538530f0e2bbdb67872b491120c5c8a7", this.nextSibling, true,"program-settings-modal"); return false;'></button><a submitid="userProgramSettingsModalConferenceEdition_apply18ec3dfeeecb5c44c1e40a40983898869" href="javascript:void(0)" onclick="javascript:loadImageElem=this;$(this.previousSibling).click()" class="btn btn-primary btn btn-default">Save</a></div></div></form></div><div class="modal-footer"><a href="Architectural-Evolution.html#" data-dismiss="modal" class="btn btn-default"><span class="glyphicon glyphicon-remove"></span> Close</a></div></div></div></div></div><div class="program-container"><div class="row" style="padding:4px 0px;"><label class="col-sm-2 control-label" style="text-align:right;">Session Program</label><div class="col-sm-10"><div class="hidable day-wrapper"><h4 class="day-header sticky-top"><div><div>Mon 2 Jun</div><p></p><div class="time-zone-info"><small class="text-muted">Displayed time zone: <strong>Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna</strong> <small class="control-time-zone"><a data-toggle="modal" href="Architectural-Evolution.html#userProgramSettings">change</a></small></small></div></div></h4><div class="hidable band hour-14"><table data-facet-date="Mon 2 Jun 2025" data-facet-date-order="250602" data-facet-room="6.0D09 (Workshop)" class="table table-condensed session-table"><colgroup width="10px"/><colgroup width="100px"/><colgroup width="10px"/><colgroup width="100%"/><tr class="session-details"><td class="track-color c9"></td><td><div class="slot-label">14:00 - 17:30</div></td><td colspan="2"><div class="session-info-in-table">AgileEAD<span class="pull-right"><a href="https://conf.researchr.org/home/<USER>/agileead-2025" target="_blank" class="text-muted navigate">AgileEAD</a></span> at <a href="https://conf.researchr.org/room/xp-2025/xp-2025-venue-6.0d09-%28workshop%29" target="_blank" class="room-link navigate">6.0D09 (Workshop)</a><br/></div></td></tr><tr class="firefox-fix"><td></td><td></td><td></td><td></td></tr><tr data-slot-id="e371206a-4ef9-40be-97ef-c98dbe00f682" class="hidable"><td class="track-color c9"></td><td class="daycolor-2 text-right"><div class="text-muted"><div class="start-time">14:00</div><strong>45m</strong></div><div class="event-type">Talk</div><span data-facet-track="AgileEAD"></span></td><td><span data-event-star="47beb5a3-a474-48ea-8a5a-fc3bc1f2f03c" title="Add event to your program"><span class="text-disabled glyphicon glyphicon-star-empty"></span></span></td><td><a href="Architectural-Evolution.html#" data-event-modal="47beb5a3-a474-48ea-8a5a-fc3bc1f2f03c">Documenting complex reusable architecture</a><div class="prog-track">AgileEAD</div><div class="performers"><a href="https://conf.researchr.org/profile/xp-2025/ademaraguiar" class="navigate">Ademar Aguiar</a><span class="prog-aff"> INESC TEC, Faculdade de Engenharia, Universidade do Porto</span></div></td></tr><tr data-slot-id="f24650f7-4cc0-4164-ac7e-d9ac262a2dd2" class="hidable"><td class="track-color c9"></td><td class="daycolor-2 text-right"><div class="text-muted"><div class="start-time">14:45</div><strong>45m</strong></div><div class="event-type">Talk</div><span data-facet-track="AgileEAD"></span></td><td><span data-event-star="b43da1d6-05ab-4890-8f85-af88291b4c8a" title="Add event to your program"><span class="text-disabled glyphicon glyphicon-star-empty"></span></span></td><td><strong><a href="Architectural-Evolution.html#" data-event-modal="b43da1d6-05ab-4890-8f85-af88291b4c8a">Architectural Evolution</a></strong><div class="prog-track">AgileEAD</div><div class="performers"><a href="https://conf.researchr.org/profile/xp-2025/eduardoguerra" class="navigate">Eduardo Guerra</a><span class="prog-aff"> Free University of Bozen-Bolzano</span></div><a href="Architectural-Evolution.html" target="_blank" class="publication-link navigate"><span class="glyphicon glyphicon-paperclip"></span> File Attached</a></td></tr><tr data-slot-id="6cb2beb8-e63f-4467-8506-8aef499f8dc7" class="hidable"><td class="track-color c9"></td><td class="daycolor-2 text-right"><div class="text-muted"><div class="start-time">15:30</div><strong>30m</strong></div><div class="event-type">Coffee break</div><span data-facet-track="AgileEAD"></span></td><td><span data-event-star="e0f3d776-55cd-4c68-ad47-630d63ce0e88" title="Add event to your program"><span class="text-disabled glyphicon glyphicon-star-empty"></span></span></td><td><a href="Architectural-Evolution.html#" data-event-modal="e0f3d776-55cd-4c68-ad47-630d63ce0e88">Coffee Break</a><div class="prog-track">AgileEAD</div><br></td></tr><tr data-slot-id="5fcfa977-d6de-48ff-a397-1f1ce3d31e39" class="hidable"><td class="track-color c9"></td><td class="daycolor-2 text-right"><div class="text-muted"><div class="start-time">16:00</div><strong>45m</strong></div><div class="event-type">Talk</div><span data-facet-track="AgileEAD"></span></td><td><span data-event-star="db404298-1df1-4d25-939d-16e034fad4c9" title="Add event to your program"><span class="text-disabled glyphicon glyphicon-star-empty"></span></span></td><td><a href="Architectural-Evolution.html#" data-event-modal="db404298-1df1-4d25-939d-16e034fad4c9">Is “Agile” EA really possible?</a><div class="prog-track">AgileEAD</div><div class="performers"><a href="https://conf.researchr.org/profile/xp-2025/beatliver" class="navigate">Beat Liver</a><span class="prog-aff"> Switch</span></div></td></tr><tr data-slot-id="0222a1d6-4735-41d3-9053-536f9573ad27" class="hidable"><td class="track-color c9"></td><td class="daycolor-2 text-right"><div class="text-muted"><div class="start-time">16:45</div><strong>45m</strong></div><div class="event-type">Other</div><span data-facet-track="AgileEAD"></span></td><td><span data-event-star="ca32aabb-8d55-4329-bb73-27f0e6a0dca4" title="Add event to your program"><span class="text-disabled glyphicon glyphicon-star-empty"></span></span></td><td><a href="Architectural-Evolution.html#" data-event-modal="ca32aabb-8d55-4329-bb73-27f0e6a0dca4">Summary, closing &amp; wrap-up</a><div class="prog-track">AgileEAD</div><div class="performers"><a href="https://conf.researchr.org/profile/xp-2025/norasleumer" class="navigate">Nora Sleumer</a><span class="prog-aff"> Vice President Swiss Informatics Society</span>, <a href="https://conf.researchr.org/profile/xp-2025/simonmoser" class="navigate">Simon Moser</a><span class="prog-aff"> CEO SolutionBoxX Ltd</span></div></td></tr></table></div></div></div></div></div></div><div class="col-sm-4 col-lg-3"><a href="https://conf.researchr.org/profile/xp-2025/eduardoguerra" class="navigate"><div class="thumbnail"><img alt="Eduardo Guerra" src="../../../../getProfileImage/eduardoguerra/091b4322-1340-432b-a926-81158533c117/small.jpg@1741267922000" class="outputimage "/><div class="caption"><h4>Eduardo Guerra<span class="pull-right"><small></small></span></h4><h5><span class="text-black">Free University of Bozen-Bolzano</span></h5></div></div></a></div></div><div id="messages-placeholder" class="alert alert-warning" style="display:none;"><a data-dismiss="alert" class="close">x</a><em>Fri 30 May 20:54</em></div></div><div id="notifications-ph" class="webdsl-placeholder"></div><div id="event-modal-loader" class="webdsl-placeholder"><form name="form_131600131703c411e65b13378d08eb1f6672b5a0259" id="form_131600131703c411e65b13378d08eb1f6672b5a0259" action="https://conf.researchr.org/eventDetailsModalByAjaxConferenceEdition" accept-charset="UTF-8" method="POST" class="hidden"><input type="hidden" name="form_131600131703c411e65b13378d08eb1f6672b5a0259" value="1" /><input type="hidden" name="context" value="xp-2025" /><input name="ae03f7f6f951d515a297b161e922205d" type="text" value="" class="inputString form-control event-id-input"/><button style="position: absolute; left: -9999px; width: 1px; height: 1px;" onclick='javascript:serverInvoke("https://conf.researchr.org/eventDetailsModalByAjaxConferenceEdition","eventDetailsModalByAjaxConferenceEdition_ia0_3c411e65b13378d08eb1f6672b5a0259", [{"name":"context", "value":"xp-2025"},],"form_131600131703c411e65b13378d08eb1f6672b5a0259", this.nextSibling, false,"event-modal-loader"); return false;'></button><a submitid="eventDetailsModalByAjaxConferenceEdition_ia0_3c411e65b13378d08eb1f6672b5a0259" href="javascript:void(0)" onclick="javascript:loadImageElem=this;$(this.previousSibling).click()" id="load-modal-action"></a></form></div><div id="event-star-form" class="webdsl-placeholder"><form name="form_509860938088b48fd14544d4239b498a2cf339e02b" id="form_509860938088b48fd14544d4239b498a2cf339e02b" action="https://conf.researchr.org/eventStarByAjaxConferenceEdition" accept-charset="UTF-8" method="POST" class="hidden"><input type="hidden" name="form_509860938088b48fd14544d4239b498a2cf339e02b" value="1" /><input type="hidden" name="context" value="xp-2025" /><input name="a0b55aa29cf9431a9461b359872014e3" type="text" value="" class="inputString form-control event-id-input"/><button style="position: absolute; left: -9999px; width: 1px; height: 1px;" onclick='javascript:serverInvoke("https://conf.researchr.org/eventStarByAjaxConferenceEdition","eventStarByAjaxConferenceEdition_ia0_88b48fd14544d4239b498a2cf339e02b", [{"name":"context", "value":"xp-2025"},],"form_509860938088b48fd14544d4239b498a2cf339e02b", this.nextSibling, false,"event-star-form"); return false;'></button><a submitid="eventStarByAjaxConferenceEdition_ia0_88b48fd14544d4239b498a2cf339e02b" href="javascript:void(0)" onclick="javascript:loadImageElem=this;$(this.previousSibling).click()" id="star-event-action"></a></form></div><div id="event-modals" class="webdsl-placeholder"></div><script type="text/javascript">(function(){
    var post_process_function = function(n){ var node=(n&&n.nodeType === 1)?n:document; addEventModalLoadOnClick(node); addStarredEventOnClick(node); };
    var original_post_process_func = ajax_post_process;
    ajax_post_process = function(){
      original_post_process_func.apply(this,arguments);
      post_process_function.apply(this,arguments);
    };
    $(document).ready( post_process_function );
  })();   
  </script><footer class="footer"><div class="container"><div class="footer-box"><div class="row"><div class="col-sm-3"><h3><a href="https://conf.researchr.org/home/<USER>" class="navigate"><span class="glyphicon glyphicon-home"></span> XP 2025</a></h3><div><a href="https://conf.researchr.org/contact/xp-2025" class="navigate"><span class="glyphicon glyphicon-envelope"></span> contact form</a></div><hr/>using <a href="https://conf.researchr.org" class="navigate">conf.researchr.org</a> (<a href="http://yellowgrass.org/roadmap/conf.researchr.org" class="navigate">v1.69.0</a>)<br/><small><a href="https://conf.researchr.org/support/xp-2025" target="_blank" class="navigate"><span class="glyphicon glyphicon-question-sign"></span> Support page</a></small><br/><small></small></div><div class="col-sm-5"><div class="row"><div class="col-sm-6"><h4>Tracks</h4><a href="https://conf.researchr.org/track/xp-2025/xp-2025-agile-training-and-education" class="navigate">Agile Training and Education</a><br/><a href="https://conf.researchr.org/track/xp-2025/xp-2025-experience-reports" class="navigate">Experience Reports</a><br/><a href="https://conf.researchr.org/track/xp-2025/xp-2025-industry-and-practice" class="navigate">Industry and Practice</a><br/><a href="https://conf.researchr.org/track/xp-2025/xp-2025-industry-and-practice-workshops" class="navigate">Industry and Practice Workshops</a><br/><a href="https://conf.researchr.org/track/xp-2025/xp-2025-keynotes" class="navigate">Keynotes</a><br/><a href="https://conf.researchr.org/track/xp-2025/xp-2025-open-space" class="navigate">Open Space</a><br/><a href="https://conf.researchr.org/track/xp-2025/xp-2025-posters" class="navigate">Posters</a><br/><a href="https://conf.researchr.org/track/xp-2025/xp-2025-research-papers" class="navigate">Research Papers</a><br/><a href="https://conf.researchr.org/track/xp-2025/xp-2025-workshops" class="navigate">Research Workshops</a></div><div class="col-sm-6"><h4>Workshops</h4><a href="https://conf.researchr.org/home/<USER>/aiandagile-2025" class="navigate">AIandAgile 2025</a><br/><a href="https://conf.researchr.org/home/<USER>/agileead-2025" class="navigate">AgileEAD 2025</a><br/><a href="https://conf.researchr.org/home/<USER>/agilepr-2025" class="navigate">AgilePR 2025</a><br/><a href="https://conf.researchr.org/home/<USER>/gohyb-2025" class="navigate">GoHyb 2025</a></div></div></div><div class="col-sm-2"><h4>Attending</h4><a href="https://conf.researchr.org/venue/xp-2025/xp-2025-venue" class="navigate">Venue: Campus FHNW</a><br><a href="https://conf.researchr.org/attending/xp-2025/travelling-to-brugg" class="navigate">Travelling to Brugg</a><br><a href="https://conf.researchr.org/attending/xp-2025/accommodation" class="navigate">Accommodation</a><br><a href="https://conf.researchr.org/attending/xp-2025/registration" class="navigate">Registration</a><br><a href="https://conf.researchr.org/attending/xp-2025/leisure-activities" class="navigate">Leisure activities in and around Brugg</a><br><a href="https://conf.researchr.org/attending/xp-2025/presenting" class="navigate">Presenting at XP2025</a><br><a href="https://conf.researchr.org/attending/xp-2025/xp2025-code-of-conduct" class="navigate">XP2025 Code of Conduct</a></div><div class="col-sm-2"><a href="https://conf.researchr.org/signup/xp-2025" class="navigate">Sign Up</a></div></div></div></div></footer></div><script type="text/javascript">(function(){
    var post_process_function = function(n){ var node=(n&&n.nodeType === 1)?n:document; let defaultplacement = $(document).scrollLeft() > 100 ? 'auto left' : 'auto top'; $(node).find('[title]').each( function(i,el){ var $e=$(el);$e.tooltip({placement: function(tt, elem){ var attval = elem.getAttribute('data-placement'); return attval ? attval : defaultplacement; }, sanitize: false, container: 'body' }) } ).on('show.bs.tooltip', function () {  let el = this; while (el && window.getComputedStyle(el).position !== 'fixed') { el = el.parentElement; } if(el) $(this).data('bs.tooltip').options.container = el;});$('.tooltip.fade.in, .ui-tooltip-content').remove();  };
    var original_post_process_func = ajax_post_process;
    ajax_post_process = function(){
      original_post_process_func.apply(this,arguments);
      post_process_function.apply(this,arguments);
    };
    $(document).ready( post_process_function );
  })();   
  </script><script type="text/javascript">jQuery(document).ready(function($) {
        $(".clickable-row").click(function() {
          var href = $(this).attr("href");
          if( window.location.href.indexOf( href ) < 0 ){
            if ($(this).hasClass('new-window') ){
              window.open( href );
            } else {
              window.document.location = href;
            }
          }
        });
    });
    </script></body></html>
