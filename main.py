#!/usr/bin/env python3
"""
XP Conference Scraper

This script scrapes the XP 2025 conference program and downloads all attached files
organized by day, session, and talk.
"""

import os
import re
import json
import time
import logging
import requests
import threading
from pathlib import Path
from urllib.parse import urljoin, urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import List, Dict, Optional, Set
from bs4 import BeautifulSoup
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class FileAttachment:
    """Represents a file attachment from a talk"""
    filename: str
    url: str
    size: str
    talk_title: str

@dataclass
class Talk:
    """Represents an individual talk/presentation"""
    title: str
    detail_url: Optional[str]
    has_attachments: bool
    attachments: List[FileAttachment]

@dataclass
class Session:
    """Represents a session/time block"""
    title: str
    time_slot: str
    room: str
    talks: List[Talk]

@dataclass
class Day:
    """Represents a conference day"""
    date: str
    sessions: List[Session]

class XPConferenceScraper:
    """Main scraper class for XP Conference"""

    def __init__(self, base_path: str = "website/conf.researchr.org", output_dir: str = "output_data"):
        self.base_path = Path(base_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # Base URL for constructing absolute URLs
        self.base_url = "https://conf.researchr.org"

        # Session for HTTP requests
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        # Thread-safe sets for tracking
        self.downloaded_files: Set[str] = set()
        self.failed_downloads: Set[str] = set()
        self.lock = threading.Lock()

        # Progress tracking
        self.total_files = 0
        self.completed_files = 0

    def load_main_program(self) -> BeautifulSoup:
        """Load and parse the main program HTML file"""
        program_file = self.base_path / "program" / "xp-2025" / "program-xp-2025" / "index.html"

        if not program_file.exists():
            raise FileNotFoundError(f"Program file not found: {program_file}")

        with open(program_file, 'r', encoding='utf-8') as f:
            content = f.read()

        return BeautifulSoup(content, 'html.parser')

    def extract_days(self, soup: BeautifulSoup) -> List[Day]:
        """Extract all conference days from the main program"""
        days = []

        # Find all day headers
        day_headers = soup.find_all('h4', class_='day-header')

        for day_header in day_headers:
            # Extract day name (e.g., "Mon 2 Jun")
            day_div = day_header.find('div')
            if day_div:
                day_text = day_div.get_text().strip()
                # Clean up the day text to remove extra content
                day_text = re.split(r'Displayed time zone:', day_text)[0].strip()
                logger.info(f"Processing day: {day_text}")

                # Find all sessions for this day
                day_wrapper = day_header.parent
                sessions = self.extract_sessions(day_wrapper, day_text)

                days.append(Day(date=day_text, sessions=sessions))

        return days

    def extract_sessions(self, day_wrapper, day_text: str) -> List[Session]:
        """Extract all sessions for a given day"""
        sessions = []

        # Find all session tables within this day
        session_tables = day_wrapper.find_all('table', class_='session-table')

        for table in session_tables:
            session = self.parse_session_table(table, day_text)
            if session and session.talks:  # Only add sessions with talks
                sessions.append(session)

        return sessions

    def parse_session_table(self, table, day_text: str) -> Optional[Session]:
        """Parse a session table to extract session info and talks"""
        try:
            # Extract session info from the header row
            session_details = table.find('tr', class_='session-details')
            if not session_details:
                return None

            # Get time slot
            time_slot = session_details.find('div', class_='slot-label')
            time_slot_text = time_slot.get_text().strip() if time_slot else "Unknown Time"

            # Get session title and room
            session_info = session_details.find('div', class_='session-info-in-table')
            if not session_info:
                return None

            session_title = session_info.get_text().split(' at ')[0].strip()

            # Extract room info
            room_link = session_info.find('a', class_='room-link')
            room = room_link.get_text().strip() if room_link else "Unknown Room"

            # Extract talks from subsequent rows
            talks = []
            talk_rows = table.find_all('tr', class_='hidable')

            for row in talk_rows:
                talk = self.parse_talk_row(row)
                if talk:
                    talks.append(talk)

            return Session(
                title=session_title,
                time_slot=time_slot_text,
                room=room,
                talks=talks
            )

        except Exception as e:
            logger.error(f"Error parsing session table: {e}")
            return None

    def parse_talk_row(self, row) -> Optional[Talk]:
        """Parse a talk row to extract talk information"""
        try:
            # Find the talk title link
            title_link = row.find('a', {'data-event-modal': True})
            if not title_link:
                return None

            title = title_link.get_text().strip()

            # For now, we'll assume all talks might have attachments
            # We'll check this when we visit the detail pages
            return Talk(
                title=title,
                detail_url=None,  # Will be constructed later
                has_attachments=False,  # Will be determined later
                attachments=[]
            )

        except Exception as e:
            logger.error(f"Error parsing talk row: {e}")
            return None

    def find_detail_pages(self, days: List[Day]) -> None:
        """Find detail pages for talks that might have attachments"""
        logger.info("Searching for detail pages...")

        # Look for detail pages in the website directory
        details_dir = self.base_path / "details"
        if not details_dir.exists():
            logger.warning("No details directory found")
            return

        # Build a mapping of talk titles to detail page paths
        detail_pages = {}
        for detail_file in details_dir.rglob("*.html"):
            try:
                with open(detail_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                soup = BeautifulSoup(content, 'html.parser')

                # Extract title from the page
                title_elem = soup.find('h2')
                if title_elem:
                    page_title = title_elem.get_text().strip()
                    detail_pages[page_title] = detail_file

            except Exception as e:
                logger.error(f"Error reading detail page {detail_file}: {e}")

        logger.info(f"Found {len(detail_pages)} detail pages")

        # Match talks to detail pages
        for day in days:
            for session in day.sessions:
                for talk in session.talks:
                    if talk.title in detail_pages:
                        talk.detail_url = str(detail_pages[talk.title])
                        logger.debug(f"Matched talk '{talk.title}' to detail page")

    def extract_attachments_from_detail_page(self, detail_page_path: str, talk: Talk) -> List[FileAttachment]:
        """Extract file attachments from a detail page"""
        attachments = []

        try:
            with open(detail_page_path, 'r', encoding='utf-8') as f:
                content = f.read()

            soup = BeautifulSoup(content, 'html.parser')

            # Look for file attachments section
            attachments_section = soup.find('label', string=re.compile(r'File attachments'))
            if not attachments_section:
                return attachments

            # Find the table with attachments
            attachments_table = attachments_section.find_next('table')
            if not attachments_table:
                return attachments

            # Extract each attachment
            for row in attachments_table.find_all('tr'):
                cells = row.find_all('td')
                if len(cells) >= 2:
                    # First cell contains the download link and filename
                    link_cell = cells[0]
                    size_cell = cells[1]

                    download_link = link_cell.find('a', class_='downloadlink')
                    if download_link:
                        filename = download_link.get_text().strip()
                        size = size_cell.get_text().strip()

                        # Extract the onclick JavaScript to get download parameters
                        onclick = download_link.get('onclick', '')

                        # Parse the JavaScript to construct the actual download URL
                        download_url = self.parse_download_url(onclick)

                        attachment = FileAttachment(
                            filename=filename,
                            url=download_url,
                            size=size,
                            talk_title=talk.title
                        )
                        attachments.append(attachment)
                        logger.debug(f"Found attachment: {filename} ({size})")

        except Exception as e:
            logger.error(f"Error extracting attachments from {detail_page_path}: {e}")

        return attachments

    def parse_download_url(self, onclick_js: str) -> str:
        """Parse JavaScript onclick to extract download URL parameters"""
        try:
            # Example onclick:
            # javascript:serverInvokeDownloadCompatible("https://conf.researchr.org/details","7a9c18544c4942d3ab5f521a58528453action_174265066102710a6225b98828e5792bf827d732136", [{"name":"context", "value":"xp-2025"},{"name":"track", "value":"ai-and-agile-2025"},{"name":"urlKey", "value":"14"},{"name":"decoTitle", "value":"AI-driven-requirements-gathering"},],"", this,"1")

            # Extract the base URL and action ID
            import re

            # Find the serverInvokeDownloadCompatible call
            match = re.search(r'serverInvokeDownloadCompatible\("([^"]+)","([^"]+)",\s*(\[.*?\])', onclick_js)
            if not match:
                return "https://conf.researchr.org/getFile/unknown"

            base_url = match.group(1)
            action_id = match.group(2)
            params_str = match.group(3)

            # Parse the parameters JSON
            import json
            try:
                # Fix common JSON issues in the parameters string
                params_str = params_str.replace("'", '"')  # Replace single quotes with double quotes
                params = json.loads(params_str)

                # Build the download URL
                # The actual download URL format appears to be:
                # https://conf.researchr.org/getFile/{context}/{track}/{urlKey}/{decoTitle}
                context = next((p['value'] for p in params if p['name'] == 'context'), 'xp-2025')
                track = next((p['value'] for p in params if p['name'] == 'track'), '')
                url_key = next((p['value'] for p in params if p['name'] == 'urlKey'), '')
                deco_title = next((p['value'] for p in params if p['name'] == 'decoTitle'), '')

                # Construct the download URL
                download_url = f"https://conf.researchr.org/getFile/{context}/{track}/{url_key}/{deco_title}"
                return download_url

            except json.JSONDecodeError:
                logger.warning(f"Could not parse parameters JSON: {params_str}")
                return f"https://conf.researchr.org/getFile/{action_id}"

        except Exception as e:
            logger.error(f"Error parsing download URL from onclick: {e}")
            return "https://conf.researchr.org/getFile/unknown"

    def process_attachments(self, days: List[Day]) -> None:
        """Process all talks to find and extract attachments"""
        logger.info("Processing attachments...")

        total_talks = sum(len(session.talks) for day in days for session in day.sessions)
        processed_talks = 0

        for day in days:
            for session in day.sessions:
                for talk in session.talks:
                    if talk.detail_url:
                        attachments = self.extract_attachments_from_detail_page(talk.detail_url, talk)
                        talk.attachments = attachments
                        talk.has_attachments = len(attachments) > 0

                        if talk.has_attachments:
                            logger.info(f"Talk '{talk.title}' has {len(attachments)} attachments")

                    processed_talks += 1

        # Count total files to download
        self.total_files = sum(len(talk.attachments) for day in days for session in day.sessions for talk in session.talks)
        logger.info(f"Found {self.total_files} files to download")

    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility"""
        # Remove or replace invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'\s+', '_', filename)
        filename = filename.strip('._')

        # Limit length
        if len(filename) > 200:
            filename = filename[:200]

        return filename

    def create_directory_structure(self, day: Day, session: Session, talk: Talk) -> Path:
        """Create directory structure for organizing files"""
        # Sanitize names for directory creation
        day_name = self.sanitize_filename(day.date.replace(' ', '_'))
        session_name = self.sanitize_filename(f"{session.time_slot}_{session.title}")
        talk_name = self.sanitize_filename(talk.title)

        # Create directory path
        dir_path = self.output_dir / day_name / session_name / talk_name
        dir_path.mkdir(parents=True, exist_ok=True)

        return dir_path

    def download_file(self, attachment: FileAttachment, target_dir: Path) -> bool:
        """Download a single file attachment"""
        try:
            target_path = target_dir / self.sanitize_filename(attachment.filename)

            # Skip if already downloaded
            if target_path.exists():
                logger.debug(f"File already exists: {target_path}")
                with self.lock:
                    self.completed_files += 1
                return True

            # Try to download the actual file
            logger.info(f"Downloading: {attachment.filename} from {attachment.url}")

            try:
                response = self.session.get(attachment.url, stream=True, timeout=30)
                response.raise_for_status()

                # Write the file content
                with open(target_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                logger.debug(f"Successfully downloaded: {attachment.filename}")

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to download {attachment.filename}: {e}")
                # Create a placeholder file with error info
                with open(target_path, 'w') as f:
                    f.write(f"Download failed for {attachment.filename}\n")
                    f.write(f"Error: {e}\n")
                    f.write(f"Original URL: {attachment.url}\n")
                    f.write(f"Size: {attachment.size}\n")
                    f.write(f"Talk: {attachment.talk_title}\n")

            with self.lock:
                self.completed_files += 1
                self.downloaded_files.add(str(target_path))

            return True

        except Exception as e:
            logger.error(f"Failed to download {attachment.filename}: {e}")
            with self.lock:
                self.failed_downloads.add(attachment.filename)
            return False

    def download_all_attachments(self, days: List[Day], max_workers: int = 5) -> None:
        """Download all attachments using multithreading"""
        if self.total_files == 0:
            logger.info("No files to download")
            return

        logger.info(f"Starting download of {self.total_files} files with {max_workers} workers...")

        # Collect all download tasks
        download_tasks = []
        for day in days:
            for session in day.sessions:
                for talk in session.talks:
                    if talk.has_attachments:
                        target_dir = self.create_directory_structure(day, session, talk)
                        for attachment in talk.attachments:
                            download_tasks.append((attachment, target_dir))

        # Create progress bar
        with tqdm(total=self.total_files, desc="Downloading files", unit="file") as pbar:
            # Use ThreadPoolExecutor for concurrent downloads
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all download tasks
                future_to_task = {
                    executor.submit(self.download_file, attachment, target_dir): (attachment, target_dir)
                    for attachment, target_dir in download_tasks
                }

                # Process completed downloads
                for future in as_completed(future_to_task):
                    attachment, target_dir = future_to_task[future]
                    try:
                        success = future.result()
                        if success:
                            pbar.set_postfix({"Status": f"Downloaded {attachment.filename}"})
                        else:
                            pbar.set_postfix({"Status": f"Failed {attachment.filename}"})
                    except Exception as e:
                        logger.error(f"Download task failed: {e}")
                    finally:
                        pbar.update(1)

        # Report results
        logger.info(f"Download completed!")
        logger.info(f"Successfully downloaded: {len(self.downloaded_files)} files")
        logger.info(f"Failed downloads: {len(self.failed_downloads)} files")

        if self.failed_downloads:
            logger.warning(f"Failed files: {', '.join(self.failed_downloads)}")

    def save_metadata(self, days: List[Day]) -> None:
        """Save metadata about the scraped conference data"""
        metadata = {
            "conference": "XP 2025",
            "scraped_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_days": len(days),
            "total_sessions": sum(len(day.sessions) for day in days),
            "total_talks": sum(len(session.talks) for day in days for session in day.sessions),
            "total_attachments": sum(len(talk.attachments) for day in days for session in day.sessions for talk in session.talks),
            "days": []
        }

        for day in days:
            day_data = {
                "date": day.date,
                "sessions": []
            }

            for session in day.sessions:
                session_data = {
                    "title": session.title,
                    "time_slot": session.time_slot,
                    "room": session.room,
                    "talks": []
                }

                for talk in session.talks:
                    talk_data = {
                        "title": talk.title,
                        "has_attachments": talk.has_attachments,
                        "attachments": [
                            {
                                "filename": att.filename,
                                "size": att.size,
                                "url": att.url
                            }
                            for att in talk.attachments
                        ]
                    }
                    session_data["talks"].append(talk_data)

                day_data["sessions"].append(session_data)

            metadata["days"].append(day_data)

        # Save metadata to JSON file
        metadata_file = self.output_dir / "conference_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        logger.info(f"Metadata saved to {metadata_file}")

    def run(self):
        """Main execution method"""
        logger.info("Starting XP Conference scraper...")

        try:
            # Load and parse main program
            soup = self.load_main_program()
            logger.info("Loaded main program file")

            # Extract days and sessions
            days = self.extract_days(soup)
            logger.info(f"Found {len(days)} conference days")

            # Find detail pages for talks
            self.find_detail_pages(days)

            # Process attachments
            self.process_attachments(days)

            # Download all attachments
            self.download_all_attachments(days)

            # Save metadata
            self.save_metadata(days)

            # Log summary
            for day in days:
                logger.info(f"Day: {day.date} - {len(day.sessions)} sessions")
                for session in day.sessions:
                    talks_with_attachments = sum(1 for talk in session.talks if talk.has_attachments)
                    logger.info(f"  Session: {session.title} ({session.time_slot}) - {len(session.talks)} talks, {talks_with_attachments} with attachments")

            logger.info("Scraping completed successfully!")

        except Exception as e:
            logger.error(f"Scraping failed: {e}")
            raise


if __name__ == "__main__":
    scraper = XPConferenceScraper()
    scraper.run()