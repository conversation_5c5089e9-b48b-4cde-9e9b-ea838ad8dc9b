"""
Helper script to install the correct WebDriver versions
Run this if you're having issues with WebDriver setup
"""

import os
import sys
import platform
import subprocess

try:
    from webdriver_manager.chrome import ChromeDriverManager
    from webdriver_manager.firefox import GeckoDriverManager
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.firefox.service import Service as FirefoxService
except ImportError:
    print("Please install the required packages:")
    print("pip install selenium webdriver-manager")
    sys.exit(1)

def setup_chromedriver():
    """Download and setup the latest compatible ChromeDriver."""
    try:
        # Install ChromeDriver
        chromedriver_path = ChromeDriverManager().install()
        print(f"ChromeDriver installed successfully at: {chromedriver_path}")
        
        # Test ChromeDriver
        service = ChromeService(chromedriver_path)
        options = webdriver.ChromeOptions()
        options.add_argument("--headless")
        driver = webdriver.Chrome(service=service, options=options)
        
        # Get Chrome version
        chrome_version = driver.capabilities['browserVersion']
        driver_version = driver.capabilities['chrome']['chromedriverVersion'].split(' ')[0]
        
        print(f"Chrome version: {chrome_version}")
        print(f"ChromeDriver version: {driver_version}")
        
        driver.quit()
        print("ChromeDriver test successful!")
        return True
    except Exception as e:
        print(f"Error setting up ChromeDriver: {e}")
        return False

def setup_firefoxdriver():
    """Download and setup the latest compatible GeckoDriver for Firefox."""
    try:
        # Install GeckoDriver
        geckodriver_path = GeckoDriverManager().install()
        print(f"GeckoDriver installed successfully at: {geckodriver_path}")
        
        # Test GeckoDriver
        service = FirefoxService(geckodriver_path)
        options = webdriver.FirefoxOptions()
        options.add_argument("--headless")
        driver = webdriver.Firefox(service=service, options=options)
        
        # Get Firefox version
        firefox_version = driver.capabilities['browserVersion']
        print(f"Firefox version: {firefox_version}")
        
        driver.quit()
        print("GeckoDriver test successful!")
        return True
    except Exception as e:
        print(f"Error setting up GeckoDriver: {e}")
        return False

def detect_browser():
    """Detect which browsers are installed on the system."""
    print("Detecting available browsers...")
    
    # Define paths for common browser installations based on OS
    if platform.system() == "Windows":
        chrome_paths = [
            os.path.join(os.environ.get('PROGRAMFILES', 'C:\\Program Files'), 'Google\\Chrome\\Application\\chrome.exe'),
            os.path.join(os.environ.get('PROGRAMFILES(X86)', 'C:\\Program Files (x86)'), 'Google\\Chrome\\Application\\chrome.exe'),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Google\\Chrome\\Application\\chrome.exe')
        ]
        firefox_paths = [
            os.path.join(os.environ.get('PROGRAMFILES', 'C:\\Program Files'), 'Mozilla Firefox\\firefox.exe'),
            os.path.join(os.environ.get('PROGRAMFILES(X86)', 'C:\\Program Files (x86)'), 'Mozilla Firefox\\firefox.exe')
        ]
    elif platform.system() == "Darwin":  # macOS
        chrome_paths = [
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
            '~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        ]
        firefox_paths = [
            '/Applications/Firefox.app/Contents/MacOS/firefox',
            '~/Applications/Firefox.app/Contents/MacOS/firefox'
        ]
    else:  # Linux and others
        chrome_paths = ['/usr/bin/google-chrome', '/usr/bin/chromium', '/usr/bin/chromium-browser']
        firefox_paths = ['/usr/bin/firefox']
    
    # Check Chrome
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"Chrome found at: {path}")
            return "chrome"
    
    # Check Firefox
    for path in firefox_paths:
        if os.path.exists(path):
            print(f"Firefox found at: {path}")
            return "firefox"
    
    # If no browser executable found, try checking with 'where' or 'which' command
    if platform.system() == "Windows":
        cmd = "where"
    else:
        cmd = "which"
        
    try:
        subprocess.check_output([cmd, "chrome"])
        print("Chrome found in PATH")
        return "chrome"
    except (subprocess.CalledProcessError, FileNotFoundError):
        try:
            subprocess.check_output([cmd, "google-chrome"])
            print("Google Chrome found in PATH")
            return "chrome"
        except (subprocess.CalledProcessError, FileNotFoundError):
            try:
                subprocess.check_output([cmd, "firefox"])
                print("Firefox found in PATH")
                return "firefox"
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("No supported browsers found in PATH")
    
    return None

if __name__ == "__main__":
    print("Setting up WebDrivers for browser automation...")
    
    # Try to detect available browsers
    browser_type = detect_browser()
    
    if browser_type == "chrome":
        print("\nChrome browser detected, setting up ChromeDriver...")
        chrome_success = setup_chromedriver()
        
        if chrome_success:
            print("\nChrome setup complete!")
        else:
            print("\nChrome setup failed. Trying Firefox as a fallback...")
            firefox_success = setup_firefoxdriver()
            
            if firefox_success:
                print("\nFirefox setup complete as a fallback!")
            else:
                print("\nBoth Chrome and Firefox setup failed. Please ensure at least one browser is installed.")
                sys.exit(1)
    
    elif browser_type == "firefox":
        print("\nFirefox browser detected, setting up GeckoDriver...")
        firefox_success = setup_firefoxdriver()
        
        if firefox_success:
            print("\nFirefox setup complete!")
        else:
            print("\nFirefox setup failed. Trying Chrome as a fallback...")
            chrome_success = setup_chromedriver()
            
            if chrome_success:
                print("\nChrome setup complete as a fallback!")
            else:
                print("\nBoth Firefox and Chrome setup failed. Please ensure at least one browser is installed.")
                sys.exit(1)
    
    else:
        print("\nNo supported browsers detected. Trying to set up both drivers anyway...")
        
        # Try Chrome first
        chrome_success = setup_chromedriver()
        if chrome_success:
            print("\nChrome setup complete! Please install Chrome browser to use it.")
        
        # Try Firefox as well
        firefox_success = setup_firefoxdriver()
        if firefox_success:
            print("\nFirefox setup complete! Please install Firefox browser to use it.")
        
        if not chrome_success and not firefox_success:
            print("\nSetup failed for both drivers. Please ensure Chrome or Firefox is installed.")
            sys.exit(1)
    
    print("\nSetup complete! You can now run main.py")
    print("python main.py")
