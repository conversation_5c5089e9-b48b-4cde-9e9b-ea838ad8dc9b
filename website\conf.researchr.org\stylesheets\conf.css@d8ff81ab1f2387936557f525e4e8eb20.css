a{
  word-break: break-word;	
}
.navbar-nav > li > a{
  color : #555;
}
ul.no-bullet{
  list-style-type: none;
}
.text-black{
  color : #000;
}

.nav-tabs>li.active>a, .nav-tabs>li.active>a:hover, .nav-tabs>li.active>a:focus{
  background-color: #f5f5f5 !important;
}
.navbar-image{
  border-radius: 50%;
  height: 40px;
  width: 40px;
  margin: -20px 0px;
}
input.select-all-input{
  width : 100%;
  line-height: 1em;
}
.selectedCnt{
	padding: 5px;
	margin: -5px;
}
.portrait-small{
  width : 110px;
  height: 110px;
}

.clickable-row{
  cursor: pointer;
}
.sortable li{
  cursor: move;
}
/* search */

.search-result{
  background-color: whitesmoke;
  margin: 2px;
  padding: 10px;
  border: 1px solid #dddddd;
}
.search-result:hover{
  outline-style: double;
  outline-width: thin;
  outline-offset: 0px;
  cursor: pointer;
  background-color: #e7e7e7;
}

.daycolor-0{ color: green !important;}
.daycolor-1{ color: darkgoldenrod !important;}
.daycolor-2{ color: cornflowerblue !important;}
.daycolor-3{ color: darkmagenta !important;}
.daycolor-4{ color: brown !important;}

.row-color-0{ background-color: #EEE9E9 !important; }
.row-color-1{ background-color: #EED5B7 !important; }
.row-color-2{ background-color: #EEFFEE !important; }
.row-color-3{ background-color: #EEE9BF !important; }
.row-color-4{ background-color: #E0EEE0 !important; }
.row-color-5{ background-color: #EEE0E5 !important; }
.row-color-6{ background-color: #EEFFFF !important; }
.row-color-7{ background-color: #EEE5DE !important; }
.row-color-8{ background-color: #DCDCDC !important; }
.row-color-9{ background-color: #DDEEDD !important; }
.row-color-10{ background-color: #EEEEFF !important; }
.row-color-11{ background-color: #8FBC8F !important; }
.row-color-12{ background-color: #B8860B !important; }

.track-color.c0{ background-color: #475ed1 !important; color: white !important; }
.track-color.c1{ background-color: #47bae8 !important; }
.track-color.c2{ background-color: #5e8c47 !important; color: white !important; }
.track-color.c3{ background-color: #5ee8ff !important; }
.track-color.c4{ background-color: #75e88c !important; }
.track-color.c5{ background-color: #8cbad1 !important; }
.track-color.c6{ background-color: #a3475e !important; color: white !important; }
.track-color.c7{ background-color: #a3a3ba !important; }
.track-color.c8{ background-color: #ba5e8c !important; }
.track-color.c9{ background-color: #baffe8 !important; }
.track-color.c10{ background-color: #d18cff !important; }
.track-color.c11{ background-color: #e85e47 !important; }
.track-color.c12{ background-color: #e8e8ff !important; }
.track-color.c13{ background-color: #ff5eff !important; }
.track-color.c14{ background-color: #ffe85e !important; }
.track-color.c15{ background-color: #47ba8c !important; }
.track-color.c16{ background-color: #5e8cd1 !important; }
.track-color.c17{ background-color: #7547ff !important; color: white !important; }
.track-color.c18{ background-color: #8c8c8c !important; }
.track-color.c19{ background-color: #8cffff !important; }
.track-color.c20{ background-color: #a3d175 !important; }
.track-color.c21{ background-color: #baba8c !important; }
.track-color.c22{ background-color: #d18c47 !important; }
.track-color.c23{ background-color: #e847d1 !important; }
.track-color.c24{ background-color: #e8ff8c !important; }
.track-color.c25{ background-color: #ffa347 !important; }
.track-color.c26{ background-color: #478cff !important; }
.track-color.c27{ background-color: #5e5e75 !important; color: white !important; }
.track-color.c28{ background-color: #7575e8 !important; }
.track-color.c29{ background-color: #8cd147 !important; }
.track-color.c30{ background-color: #a38ce8 !important; }
.track-color.c31{ background-color: #ba755e !important; }
.track-color.c32{ background-color: #d18ca3 !important; }
.track-color.c33{ background-color: #e8a38c !important; }
.track-color.c34{ background-color: #ff5e75 !important; }
.track-color.c35{ background-color: #47758c !important; color: white !important; }
.track-color.c36{ background-color: #5ea3a3 !important; }
.track-color.c37{ background-color: #75e8d1 !important; }
.track-color.c38{ background-color: #a35ed1 !important; }
.track-color.c39{ background-color: #ba47ba !important; color: white !important; }
.track-color.c40{ background-color: #d1baba !important; }
.track-color.c41{ background-color: #e8ba5e !important; }
.track-color.c42{ background-color: #ffd18c !important; }
.track-color.c43{ background-color: #47ffe8 !important; }
.track-color.c44{ background-color: #8c7547 !important; color: white !important; }
.track-color.c45{ background-color: #a3a347 !important; }
.track-color.c46{ background-color: #d14775 !important; color: white !important; }
.track-color.c47{ background-color: #e8a3e8 !important; }
.track-color.c48{ background-color: #ffe8ba !important; }
.track-color.c49{ background-color: #5ee847 !important; }
.track-color.c50{ background-color: #8cffa3 !important; }
.track-color.c51{ background-color: #baff47 !important; }
.track-color.c52{ background-color: #e8ffd1 !important; }
.track-color.c53{ background-color: #47e8ba !important; }
.track-color.c54{ background-color: #8ca3ff !important; }
.track-color.c55{ background-color: #bad1d1 !important; }
.track-color.c56{ background-color: #ff47a3 !important; }
.track-color.c57{ background-color: #5ed175 !important; }
.track-color.c58{ background-color: #a3d1ff !important; }
.track-color.c59{ background-color: #e875ba !important; }
.track-color.c60{ background-color: #7547a3 !important; color: white !important; }
.track-color.c61{ background-color: #d147ff !important; }
.track-color.c62{ background-color: #47a35e !important; }
.track-color.c63{ background-color: #a3e8ba !important; }
.track-color.c64{ background-color: #47ff5e !important; }
.track-color.c65{ background-color: #d1e875 !important; }
.track-color.c66{ background-color: #a375a3 !important; }
.track-color.c67{ background-color: #8cff5e !important; }
.track-color.c68{ background-color: #d1d147 !important; }
.track-color.c69{ background-color: #ffbaff !important; }
.track-color.c70{ background-color: #75ba5e !important; }
.track-color { color: black; }
.track-color a{ color: inherit; }


.session-table{
  margin-bottom: 4px;
  margin-top: 0px;
}
.prog-track{
	display: none;
}
.blended-session .prog-track{
/*     max-height: 33px; */
/*     overflow: hidden; */
/*     word-break: break-all; */
    font-size: 85%;
    color: #777;
    display:block;
}

.btn-room{
  word-break: break-word;
  white-space: normal;
  background-color: #d6e3f0;
  color: #0f4c80;
  width: 100%;
  height: 100%;
  font-size: 12px;
  font-weight: bold;
}

.owl-top {
  max-height: 200px;
  width: 200px;
}
.highlight-carousel-item{
  margin: 0px 3px;
  margin-bottom: -20px;
}
.owl-item > .thumbnail > a{
  color: inherit !important;
  text-decoration: none;
}
.owl-item .thumbnail {
  margin-right: 4px;
}

.inner-link:hover{
  color: #428bca;
}

.image-in-gallery{
  max-width: 100%;
}
.thumbnail-image{
  max-width: 100px;
  max-height: 100px;
}

.no-bottom-margin{
  margin-bottom: 0px !important;
}

a.overview-page-true{
  color: inherit !important;
  font-weight: bold;
}
a.overview-page-false{
  color: inherit !important;
}

/*footer*/
html, body{
  height: 100%;
  margin: 0pt;
}
.frame{
  display: table;
  table-layout: fixed;
  height: 100%;
  width: 100%;
}
.footer{
  background-color: #f8f8f8;
  border-top: 1px solid #E5E5E5;
  width:100%;
  display: table-row;
  height: 1px;
  color: #697e91;
  font-size: 13px;
}
footer .container, footer .container-fluid{
  margin-top: 30px;
  margin-bottom: 30px;
}

footer a, a:hover{
  color: inherit;
}
footer h1, footer h2, footer h3, footer h4, footer h5{
  display: block;
  color: #566877;
}
footer img{
  max-width:100%;
}
html{
  height:100%;
}
#content{
  height:auto;
  margin-bottom: 25px;
}
.container-fluid{
  max-width: 1800px;
}

.modal-dialog-center{
  margin-top: 25%;
}
.modal-help .modal-dialog{
  width: 90%;
}
body.modal-open{
  overflow: visible;
}
.manage-page .panel{
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}
#embedWidget .panel{
  box-shadow: initial;
}
/*Accordion style list groups*/
.panel-group .list-group{
  margin-bottom: 0;
}

.panel-group > .panel > .panel-collapse > .list-group > .list-group-item{
  border-radius: 0;
  border-left: none;
  border-right: none;
}

.panel-group > .panel > .panel-collapse > .list-group > .list-group-item:last-child{
  border-bottom: none;
}
.panel-group .panel{
  box-shadow: initial;
}
.tab-pane{ padding-top: 10px; }
.manage-page .tab-pane{
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  border-radius: 0px 0px 5px 5px;
  padding: 10px;
}

/*.nav-tabs{
  margin-bottom: 0;
} */
/*edit elem*/
.navbar-edit{
  background-color: #B90000;
  border: 2px solid #E5E5E5;
  border-radius: 50%;
  position: absolute !important;
  right: 25%;
  top: 45px;
  z-index: 100;
  font-size: 15px;
  height: 45px;
  width: 45px;
  color: white !important;
  padding: 11px 12px;
}

.navbar-edit:hover{
  background-color: #A52828 !important;
}

.edit-link {
  background-color: #888888;
  border-radius: 50%;
  font-size: 11px;
  color: white !important;
  padding: 7px;
}
.edit-link:hover {
  background-color: #347ab7;
}

/*multilevel bootstrap submenu*/
.marginBottom-0 {margin-bottom:0;}

.dropdown-submenu{position:relative;}
.dropdown-submenu>.dropdown-menu{top:0;left:100%;margin-top:-6px;margin-left:-1px;-webkit-border-radius:0 6px 6px 6px;-moz-border-radius:0 6px 6px 6px;border-radius:0 6px 6px 6px; padding-right: 8px;}
.dropdown-submenu>a:after{display:block;content:" ";float:right;width:0;height:0;border-color:transparent;border-style:solid;border-width:5px 0 5px 5px;border-left-color:#cccccc;margin-top:5px;margin-right:-10px;}
.dropdown-submenu:hover>a:after{border-left-color:#555;}
.dropdown-submenu.pull-left{float:none;}.dropdown-submenu.pull-left>.dropdown-menu{left:-100%;margin-left:10px;-webkit-border-radius:6px 0 6px 6px;-moz-border-radius:6px 0 6px 6px;border-radius:6px 0 6px 6px;}

/*Multi-column bootstrap menu*/
.dropdown-menu.columns-1 {min-width: 200px;}
.dropdown-menu.columns-2 {min-width: 400px;}
.dropdown-menu.columns-3 {min-width: 600px;}
.multi-column-dropdown li {padding: 0px 0px 3px 8px;}
.multi-column-dropdown li.dropdown-submenu{padding-right: 8px;}
.multi-column-dropdown li.dropdown-header {padding-top: 6px; padding-left: 3px; }
.multi-column-dropdown li.dropdown-header:first-child {padding-top: 3px; }
.multi-column-dropdown {list-style: none; padding: 3px 5px 3px 5px;}
.multi-column-dropdown li a {display: block;clear: both;line-height: 1.428571429;color: #333;}
.multi-column-dropdown li a:hover {
  text-decoration: none; color: #262626; background-color: #f5f5f5;
}

/*start customization to add border and padding*/
.multi-column-dropdown li>a{
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: -2px;
    padding: 2px 0px 3px 5px;
    margin-top: -2px;
    margin-left: -5px;
    margin-right: 0px;
}

.multi-column-dropdown li:last-child>a{
    border-bottom: none;
    margin-bottom: inherit;
}
.multi-column-dropdown li a:focus{
  text-decoration: inherit;
}
.nav .multi-column-dropdown .open>a, .nav .multi-column-dropdown .open>a:focus, .nav .multi-column-dropdown .open>a:hover {
    border-bottom-color: #e5e5e5;
}
/*end customization to add border and padding*/
@media (max-width: 767px){
   .dropdown-menu.multi-column {min-width: 240px !important; overflow-x: hidden;}
  .multi-column-dropdown li   {padding: 0px 0px 0px 0px;}
  .multi-column-dropdown li a {padding: 5px 15px 5px 25px;}
}
a.dropdown-toggle {
    cursor: pointer !important;
}
.menu-advertised-event{
	margin-left: 5px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
}
.advertise-events-panel select {
    height: 2em;
    max-width: 400px;
    margin-right: 3px;
}
.advertise-events-panel button{
	
}
.all-tracks a{ display: inline-block; }

/*supporters*/
.supporters-row{
  height: 100px;
  margin-bottom: 15px;  
  border-bottom: 1px solid #faebcc;
}

span.supporter-logo img{
  max-width: 150px;
  max-height: 70px;
  margin: 10px;
}

/*other sizing on track page*/
.supporters-row span.supporter-logo img{
  max-width: 80%;
  max-height: 70px;
  margin: 0px 0px 10px 0px;
}

@media (min-width: 768px) and (max-width: 991px){
  span.supporter-logo img{
    max-width: 110px;
    max-height: 51px !important;
    margin: 10px;
  }
  /*other sizing on track page*/
  .supporters-row span.supporter-logo img{
    max-width: 80%;
    margin: 0px 0px 5px 0px;
  }
  .supporters-row{
    height: 75px;
  }
}

span.supporter-logo{
	display: block;
}

/*badges*/
span.output-badge{
  margin: 5px;
  max-width: 150px;
  max-height: 100px;
}
span.output-badge img{
  max-width: inherit;
  max-height: inherit;
}
span.output-badge .label{
  font-family: monospace;
  font-weight: normal;
  font-variant: small-caps;
  font-size: 100%;
}

/*time table*/
td.timeline-col{
  /* background-color: #dcdcdc; */
  padding: 5px 0px !important;
  /* max-width: 0px; */
  min-width: 25px; /*force display of gap when parallel slots have start/end time close to each other */
}
.event-elem{
  min-height: 80px !important;
  min-width: 175px;
}
.track-elem, .edition-elem{
  min-height: 80px !important;
  min-width: 105px;
}
.five-min-steps>th{
  padding: 0px !important;
  border: none;
  min-width: 13px; /*force parallel slots with different start and/or end time to misalign*/ 
}
.timeline-elem{
  border: 1px solid;
  border-radius: 5px;
  background-color: aliceblue;
  border-color: darkgrey;
  margin: 1px;
  padding: 3px;
  min-height: 60px;
  font-size: 85%;
}
.multiple .timeline-elem{
  min-height: 0px !important;
}
.timeline-table{
  /* table-layout: fixed; */
  *margin-left: -100px;/*ie7*/
  /*  width: 1600px;*/
}
.column-timeline-table {
  table-layout: fixed;
  min-width: 100%;
  width: 100%;
}
.horizontal-overflow .column-timeline-table{
  width: auto;
}
.column-timeline-table tr th {
  text-align: center;
  overflow-wrap: anywhere;
  min-width: 140px;
  
  /*stick to top with white transparent background*/
  position: -webkit-sticky;
  position: sticky;
  top: -1px;
  background-color:#ffffffc0;  
  z-index: 10;
  margin-bottom: 100px; /*don't show at bottom of table when other sticky day heading starts to scroll*/
}
.column-timeline-table tr th:first-child {
  width: 80px;
  min-width: 80px;
}
.column-timeline-table tr {
  height: 40px;
}
.column-timeline-table td {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0;
  vertical-align: top;
  min-height: 100%;
}
.column-timeline-table td .modal-dialog {
  white-space: initial;
  color: initial;
}
td.column-timeline-table-time, .th.column-timeline-table-time {
  vertical-align: top;
  text-align: center;
  padding-top: 0;
  white-space: normal;
  overflow-wrap: anywhere;
}
.column-timeline-table td, .column-timeline-table tr {
  border-top: 0;
}
.column-timeline-table tr > td > div {
  border-top: 1px dashed #e0dfdf;
}
.column-timeline-table tr:nth-child(4n+1) > td > div,
.column-timeline-table tr > td > div.hidable {
  border-top: 1px solid #ccc;
}
table.column-timeline-table {
  height: 1px;
}
.column-timeline-table tbody,
.column-timeline-table div.hidable,
.column-timeline-table .band,
.column-timeline-table div.webdsl-placeholder,
.column-timeline-table div.event-elem {
  height: 100%;
}

.column-timeline-table div.event-elem {
  min-height: 0px !important;
  min-width: 0px;
/*   display: flex;
  flex-direction: column; */
  margin: 0;
  font-size: 100%;
  border-radius: 0;
  border: 0;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
}

.column-timeline-table .event-elem > div:not(.overlapping-slots),
.column-timeline-table .overlapping-slots > .track-color > div {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.column-timeline-event-star {
    width: 22px;
    padding-left: 5px;
    padding-right: 5px;
    /* display: inline-block; */
    /* vertical-align: top; */
}
.column-timeline-event-star + a {
  width: 100%;
  margin-left: -22px;
  padding-left: 22px;
  display: block;
  height: 100%;/* float: right; */
}
/* .column-timeline-event-star + .webdsl-placeholder > a {
  display: block;
  height: 100%;
} */
.column-timeline-event-info .lim-height{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical !important;
  white-space: initial;
  -webkit-line-clamp: 1;
}
.lim-height.lim-1l{ -webkit-line-clamp: 1; }
.lim-height.lim-2l{ -webkit-line-clamp: 2; }
.lim-height.lim-3l{ -webkit-line-clamp: 3; }
.lim-height.lim-4l{ -webkit-line-clamp: 4; }
.lim-height.lim-5l{ -webkit-line-clamp: 5; }
.lim-height.lim-6l{ -webkit-line-clamp: 6; }
.lim-height.lim-7l{ -webkit-line-clamp: 7; }
.lim-height.lim-8l{ -webkit-line-clamp: 8; }

/* .column-timeline-event-1l, .column-timeline-event-2l {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.column-timeline-event-2l{
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
  white-space: initial;
}
 */


.mirror-label{
  background-color: #777;
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
}
.timeline-elem .mirror-label{
  background-color: #77777770;
}
.timeline-elem.event-elem span[data-event-star]{
  float: right;
}
span[data-event-star]{
  cursor: pointer;
}

.block-with-text {
  /* hide text if it more than N lines  */
  overflow: hidden;
  /* for set '...' in absolute position */
  position: relative; 
  /* use this value to count block height */
  line-height: 1.2em;
  /* max-height = line-height (1.2) * lines max number (3) */
  max-height: 3.6em; 
  /* fix problem when last visible word doesn't adjoin right side  */
  text-align: justify;  
  /* place for '...' */
  margin-right: -1em;
  padding-right: 1em;
}
/* create the ... */
.block-with-text:before {
  /* points in the end */
  content: '...';
  /* absolute position */
  position: absolute;
  /* set position to right bottom corner of block */
  right: 0;
  bottom: 0;
}
/* hide ... if we have text, which is less than or equal to max lines */
.block-with-text:after {
  /* points in the end */
  content: '';
  /* absolute position */
  position: absolute;
  /* set position to right bottom corner of text */
  right: 0;
  /* set width and height */
  width: 1em;
  height: 1em;
  margin-top: 0.2em;
  /* bg color = bg color under block */
  background: white;
}
.output-badge.col-badge{margin: 0px 1px 4px 0px ; max-height: 30px; float: left; max-width: 150px;}

/*equally divide height for overlapping events*/
.column-timeline-table .overlapping-slots {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  /*Safari fix for growing outside column height*/
 /* max-height: 20px; */
}
.overlapping-slots>div.track-color {
 flex-grow: 1;
}

.filtered-event-background td {
  padding: 0;
}

table.filtered-event-background{
  height: 100%;
  width: 100%;
  display: none;
}
.filtered-event-background td {
  border: 0;
  border-top: 1px dashed #e0dfdf;
}
.event-start-0 tr:nth-child(4n+1),
.event-start-5 tr:nth-child(4n),
.event-start-10 tr:nth-child(4n+3),
.event-start-15 tr:nth-child(4n+2),
.border-top-solid {
  border-top: 1px solid #ccc;
}
.column-timeline-table tr > td > div.border-top-dashed {
  border-top: 1px dashed #e0dfdf;
  display: none;
}
.hidable[data-is-visible="false"] + .border-top-dashed {
  display: block;
}
.hidable[data-is-visible="false"] + table.filtered-event-background{
  display: table;
}
.auto-scroll{
  margin-top: -15px;
}
table .auto-scroll{
  margin-top: 0px;
}
.column-timeline-table .auto-scroll, .column-timeline-table .auto-scroll-upcoming {
  margin-top: 20px;
}

.column-timeline-table .nowable.auto-scroll-upcoming {
  display: none;
}
@media (max-width: 767px) {
  .column-timeline-table tr th:first-child {
    width: 35px;
    min-width: 35px;
  }

  .column-timeline-table .col-badge {
    font-size: 0px;
  }
  .column-timeline-table .col-badge:before {
    display: block;
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    content: "\e044";
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 14px;
  }

  .column-timeline-table div.event-elem,
  .column-timeline-table-time,
  .timeline-time-header {
    font-size: 80%;
  }

  .program-container .day-wrapper {
    margin-left: -15px;
    margin-right: -15px;
  }

  .column-timeline .day-wrapper {
    border-left: 0;
  }

  .column-timeline .day-header {
    margin-left: 0;
    padding-left: 10px;
    padding-right: 10px;
  }
}

.label-neutral{
  background-color: #bbb;
}
td.timeline-first-col{
  font-size: 85%;
  height: 90px;
}
.timeline-first-col{
  position:absolute;
  left:0;
  width:120px;
  min-width: 120px;
  margin-top: -1px;
}
th.timeline-header{
  /*  min-width: 40px; */
  border-left-width: 1px;
  border-left-style: dashed;
  border-left-color: #dcdcdc;
  padding-left: 1px !important;
}
th.timeline-30m-header{
  min-width: 40px;
}
th.timeline-15m-header{
  min-width: 40px;
}
th.hour-header{
  border-left-style: solid;
  border-left-width: 2px;
  font-weight: normal;
}
th.quarter-header{
  font-weight: lighter;
}
.timeline-wrap-inner{
  overflow: auto;
  /*overflow: -moz-scrollbars-horizontal;*/
  overflow-x: auto;
  margin-left:120px;
}
.auto-scroll, .auto-scroll-upcoming{
  display: block;  
  background-color: #337AB7;
  position: absolute;
  left: 0;
  border-radius: 0px 20px 20px 0px;
  padding-left: 5px;
  margin-left: 0px;
  padding-right: 15px;
  transition: all 0.8s ease-out 0s;
}
.hidable.day-wrapper>.auto-scroll {
    z-index: 2;
}
.auto-scroll:before{
  content: "Now";
  font-size: 12px;
  color: white;
}
.auto-scroll-upcoming:before{
/*   content: "Next"; */
  font-size: 12px;
  color: white;
}
.auto-scroll-upcoming{
  background-color: #5bb85cd1 !important;
  color: white;
  font-size: 12px;
  margin-top: 17px;
/*   padding-left: 2px; */
/*   padding-right: 3px; */
  padding: 2px 3px 2px 2px;
}

.detailed-timeline .auto-scroll:before, .session-timeline .auto-scroll:before{
  content: "Today";
}
.scroll-to-now-btn{
  display: none;
  position: fixed;
  bottom: 10px;
  right: 10px;
  padding: 2px;
  z-index: 10;
}
.scroll-to-upcoming-btn{
  display: none;
  position: fixed;
  bottom: 10px;
  right: 48px;
  padding: 2px;
  z-index: 10;
}
.hide-past-events-btn {
    display: none;
    position: fixed;
    bottom: 10px;
    left: 0px;
    padding: 2px;
    z-index: 10;
}
.tz-floating-link {
    position: fixed;
    left: 4px;
    bottom: 10px;
    z-index: 10;
    width: 100%;
}
table.during-conference:not([data-facet-past]) a{
  color: #808080;
}
body#program .scroll-to-now-btn, body#program .scroll-to-upcoming-btn {
  display: inline-block;
}
body#program2 .scroll-to-now-btn, body#program2 .scroll-to-upcoming-btn {
  display: inline-block;
}
td, th{
  vertical-align: inherit;
  border-top: 1px solid #ccc;
}
.program-container .control-time-zone{
	display: inline;
}
.prog-aff{
    color: #929292;
    font-size: 85%;
}
.timeline-wrap-outer{
  position: relative;
}
.facet-placeholder{
  margin-bottom: 7px;
}
.facet-placeholder .btn {
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
  border-radius: 7px;
  border: 0px;
  background-color: #d6e3f0;
  margin-right: 5px;
  margin-bottom: 5px;
  color: #0f4c80;
  font-weight: bold;
}
.facet-false{
  /* background-color: #f5f5f5; */
  border-style: outset;
}
.facet-true{
  color: #fff !important;
  background-color: #f0ad4e !important;
  border-color: #eea236 !important;
  font-weight: bold;
  border-style: inset;
}
.past-facets {
	margin-top: 7px;
	min-height: 27px;
}
.past-facets .no-facets{
	display: none;
}
.no-facets{
  margin-bottom: 5px;
  color: #a9a9a9;
}
.facet-placeholder.affiliations .facet-false {
    display: none;
}
.session-table .start-time{
    margin-left: 7px;
    color: #b7b7b7;
    float: left; 
}
/* .session-table .start-time{
  display: none;
  color: #bbb;
}
.session-table tr[data-is-visible="false"] + tr>td .start-time{
  display: inline-block;
  margin-right: 5px;
}
 */
span.hardwraps-info{
  font-size: 12px;
  color: #777;
}
.session-info-in-table{
  min-height: 40px;
  font-weight: bold;
}
.session-info-in-table p, .edit-session-info{
 font-weight: initial;
}

.embed-container{
  position: relative;
}

.embed-container iframe,
.embed-container object,
.embed-container embed{
  width: 100%;
}
.edition-row a{
  color: black;
}
.edition-row {
  word-break: break-word;
}
.performers{
  font-size: 85%;
}
.performers a{
  color: inherit;
}
.past{ color: rgba(169, 68, 66, 0.71); }
.important-dates-in-sidebar .text-success{
  background-color: #dff0d8;
  color: inherit !important;
}
.important-dates-in-sidebar .past{
  background-color: #f2dede;
}
.important-dates-in-sidebar .text-warning{
  background-color: #fcf8e3;
}
.horizontal-radio .radio{
  min-height: 0px !important;
  padding-top: 0px !important;
}
label.radio {
  font-weight: inherit; /*bootstrap renders all label in bold, we only want selected item in bold*/
}

.indent{
  margin-left: 17px !important;
}

/* carousel */

.carousel{ margin-top : -21px; margin-bottom: 60px; }
.panel-body .carousel{ margin-top: 0px; }
.carousel-control.left{ background-image : none; }
.carousel-control.right{ background-image : none; }

.carousel .item{ width: 100%; height: 360px; }

.fill{
  min-width: 100%;
  background-position: center;
  background-size: cover !important;
  height: 100%;
}

@media (max-width: 767px){
  .carousel-inner .item {height: 240px !important;}
}
@media (min-width: 1440px){
  .carousel-inner .item {height: 420px !important;}
}

/* start carousel fade */

.carousel-fade .carousel-inner .item{
  opacity: 0;
  transition-property: opacity;
}

.carousel-fade .carousel-inner .active{
  opacity: 1;
}

.carousel-fade .carousel-inner .active.left,
.carousel-fade .carousel-inner .active.right{
  left: 0;
  opacity: 0;
  z-index: 1;
}

.carousel-fade .carousel-inner .next.left,
.carousel-fade .carousel-inner .prev.right{
  opacity: 1;
}

.carousel-fade .carousel-control{
  z-index: 2;
}

@media all and (transform-3d),
(-webkit-transform-3d){
  .carousel-fade .carousel-inner > .item.next, .carousel-fade .carousel-inner > .item.active.right{
    opacity: 0;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  .carousel-fade .carousel-inner > .item.prev,
  .carousel-fade .carousel-inner > .item.active.left{
    opacity: 0;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  .carousel-fade .carousel-inner > .item.next.left,
  .carousel-fade .carousel-inner > .item.prev.right,
  .carousel-fade .carousel-inner > .item.active{
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
/* end carousel fade */
.text-disabled{ color: #bbb;}
.text-gold{ color: #e90;}

.column-timeline-table .text-disabled{
	/*text is displayed against background color with contrasting color*/
  color: inherit;
  opacity: 0.5;
}

.publication-link{ font-size: 85%; margin-right: 4px;  }
.attribution{
  position: absolute;
  bottom: 0px;
  right: 0px;
  margin: 3px;
  color: white;
  font-size: 78%;
  opacity: 0.7;
  filter: alpha(opacity=70); /* For IE8 and earlier */
  background-color: black;
  padding: 3px;
}
.attribution:hover{
  opacity: 0.9;
  filter: alpha(opacity=90); /* For IE8 and earlier */
}
.checkbox-set-element label {
    margin-left: 3px;
}
.attribution a{
  color: inherit;
}
a.memberlink{
  color: inherit;
}
.carousel-control.right{
  margin-bottom: 25px
}

tr.session-details>td{
  background-color: #F9F9F9;
}
td.day-column{
  background-color: #f9f9f9 !important;
  border-top: none !important;
}
td.day-column h4{
  margin-left: -15px;
}
tr.firefox-fix td{
  padding: 0px !important;
}
@-moz-document url-prefix(){
  .drag{
    height: auto !important;
  }
}
@media print{
  .logobar, footer, .page-header a{
    display:none !important;
  }
}
.doc-index-entry .btn{
  display: none;
}
.doc-index-entry:hover .btn{
  display: initial;
}
blockquote{
  font-size: 14px !important;
}
.blog-link{
  position: absolute;
  right: 3px;
  top: 6px;
  box-shadow: 2px 3px 4px #000;
  z-index: 100;
}
#home .blog-link{
  position: fixed;
}
.poster-info{
  margin-top: 25px;
}
.poster-info ul{
  margin-top: 5px;
  padding-left: 17px;
  list-style-type: square;
}
.blog-container{
  padding: 15px 60px 15px 60px;
  margin-bottom: 17px;
  border-left: 8px solid #337ab7;
  border-right: 9px solid #337ab7;
  border-top: 1px solid #337ab7;
  border-bottom: 1px solid #337ab7;
}
.blog-container h2 {
    color: #337ab7 !important;
}
.blog-content {
	background-color: #F3F3F4;
  padding: 16px;
  margin-top: 34px;
}
.blog-content img{
	max-width: 100%;
}
.order-list.order-only .ui-icon.ui-icon-close{
  display: none;
}
.ui-sortable{
	padding: 6px;
  border: 1px solid #ededed;
  min-height: 50px;
  overflow-y: visible; /*fixes jumping helper when there is a parent with position relative*/ 
  overflow-x: hidden;
}
.ui-state-highlight { height: 50px;}
.order-list li {
	margin-bottom: 7px;
}
/* .edit-list-order li{
  width: initial !important;
} */
.save-bar{
  position: fixed;
  bottom: 2px;
  width: 100%;
  right: 0px;
  z-index: 10;
}
.save-bar-content{
  background-color: rgba(119,119,119,0.4);
  padding: 4px;
}
/* .manage-session-tab{
  font-size: 12px;
  max-width: 100px;
  padding: 2px !important;
  margin: 2px;
} */
.ui-datepicker{
  z-index: 11 !important;
}
.day-header{
  padding: 8px 0px 6px 10px;
  background-color: #f5f5f5 !important;
  margin-bottom: 10px;
  margin-left: -19px;
  margin-top: 0px;
}

.sticky-top{
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1;
}

.conf-date-title{
    font-size: 75%;
    font-weight: bold;
    margin-bottom: 3px;
    color: #777;
}
.day-wrapper{
  border-left: 19px solid #f5f5f5;
  display: block;
  margin-top: 10px;
  margin-bottom: 5px;
  min-width: 100%;
}

/*This makes the day heading take full width when the column timeline table overflows*/
.horizontal-overflow .day-wrapper{
  display: inline-block;
}

.remove-top-right{
  position: absolute;
  top: 10px;
  right: 40px;
}
.ui-state-default .btn-danger{
  color: white !important;
}
.help-link{
  position: fixed;
  right: 10px;
  bottom: 10px;
  z-index: 100;
}
.doc-item img{
  max-width: 300px !important;
}
.doc-item img:hover{
  max-width: 100% !important;
}
.support-option{
  margin-bottom: 20px;
}
.support-option a{
  display: block;
  color: initial;
}
.support-option .glyphicon{
  color: #566877;
  font-size: 100px;
  margin-right: 10px;
}
.support-info{
  display: inline-block;
  vertical-align: middle;
}
.contact-form{
  display: none;
}
/* .elem-preview{
	position: relative;
} */
.elem-preview-cont{
  display: inline-block;
  width: 95%;
  max-height: 200px
}
.elem-preview{
  max-height: 320px;
  width: 200%;
  overflow: auto;
  transform: scale(0.5);
  margin-bottom: -170px;
  height: 300px;
  transform-origin: top left;
}
.other-track{
	font-weight: normal;
}

textarea.inline {
    min-height: 1em !important;
    max-width: 90% !important;
    min-width: 50px !important;
    display: inline;
    height: 2.5em;
}
.small-input input{
	max-width: 150px;
}

.filterHide{
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    display: none !important;
}
.facet-placeholder.affiliations .facet-false.filterShow{
	-webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
  display: inline-block !important;
}
img.og-image{
  max-width: 200px;
  max-height: 200px;
  float: right;
  margin-top: 4px;
  margin-right: 5px;
  border: 1px solid #cccccc;
  padding: 5px;
}
.select2 {
  width: 100% !important;
}
td .select2{
  /* fixes inner absolute element to grow beyond original page width*/
  position: relative !important;
}
.select2-container--default .select2-selection--single .select2-selection__arrow{
  top: 3px !important;
}
.select2-container--default .select2-selection--single{
  height: 34px !important;
  padding-top: 2px !important;
  border-color: #ccc !important;
}
div#auto-continue-message {
	display: none;
  position: fixed;
  z-index: 10;
  right: 20%;
  top: 50%;
  left: 30%;
  /* width: 100%; */
  text-align: center;
}
span#auto-continue-cd {
    font-weight: bold;
}
.notifyjs-bootstrap-base{
	white-space: normal !important;
}
a.anchor-link{
  color: inherit;
  text-decoration: inherit;
}
a.anchor-link:hover:before{
  content: "\1F517";
  color: #0000006e;
  position: absolute;
  left: -4px;
  font-size: 12px;
}
html {
  scroll-behavior: smooth;
}
#maintenance-message{
  width: 100%;
  padding: 8px;
  position: fixed;
  background-color: #6b72b2;
  bottom: 0;
  color: white;
  text-align: center;
  font-size: 15px;
  z-index: 10;
}
#maintenance-message-close{
  color: white;
  margin-right: 10px;
  float: right
}
#dateDiffTZWarning{
  display: none;
}
.time-zone-info{
	display: inline-block;
}
.time-zone-diff-date{
	display: block;
  margin-bottom: 10px;
}
.time-zone-warning{
	display:none;
}
.time-zone-warning .control-time-zone:before{
	content: " - ";
	font-size: 14px;
}
.column-timeline-table tr:nth-child(n+1) .column-timeline-table-time.hour-0{
  position: -webkit-sticky;
  position: sticky;
  top: -1px;
  z-index: 10;
  overflow: unset;
}
.day-info{
	display: block;
	font-weight: bold;
	padding-top: 10px;
}
.column-timeline-table tr:nth-child(1) td .day-info{
	display: none;
}
.column-timeline-table tr:nth-child(n+1) td.hour-0 .hour-info{
	display: none;
}

td.hour-0 .day-info{
  background-color: white;
  /* padding-bottom: 10px; */
  min-height: 60px;
}
.time-zone-warning .alert{
	margin-bottom: -5px;
	margin-top: 5px;
}
.time-zone-warning.visible{
	display:block;
}
.add-top-margin {
    margin-top: 3px;
}
.role{
  font-weight: bold;
}
.band{
  -webkit-transition: opacity .8s ease-in-out;
  -moz-transition: opacity .8s ease-in-out;
  -ms-transition: opacity .8s ease-in-out;
  -o-transition: opacity .8s ease-in-out;
  transition: opacity .8s ease-in-out;
}
.band:hover{
  opacity: unset;
}
.band:focus-within{
  opacity: unset;
}
.flip{
  -moz-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
  filter: FlipH;
  -ms-filter: "FlipH";
}
a[data-toggle=collapse]:hover, a[data-toggle=collapse]:focus {
  text-decoration: unset;
}
/* .facet-container a[data-toggle=collapse]{
  display: block;
} */
.facet-indicator .badge {
    margin-left: 4px;
}
#affiliation-filtering{
	margin-bottom: 3px;
}
.index-letter {
    position: absolute;
    font-size: large;
    font-weight: bold;
    color: #c5c5c5;
    margin-left: -5px;
}
.filterShow .index-letter {
    display: none;
}
.hide-when-no-update{
  display: none;
}
.show-when-no-update{
 display: block;
}
.modal .input-with-preview>.col-sm-6{
	width: 100%;
}
.info-participants {
    display: block;
    position: relative;
    overflow: auto;
}

.info-participants * {max-width: 100%;}
.event-modals>div>a{ /*don't display modal links*/
	display:none;
}
.event-type {
    font-style: italic;
    color: #777;
}
.slot-label{
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
    color: #fff !important;
    text-align: center;
    white-space: nowrap;
    vertical-align: top;
    background-color: #555 !important;
    border-radius: 10px;
    margin-top: 1px;
}
.profile-in-admin-table p {
    margin: 0px;
    max-height: 100px;
    overflow: auto;
}
.profile-item{
  margin-bottom: 10px;
}
.profile-item-heading{
  float: left;
  font-weight: bold;
  margin-right: 5px;
}
.in-memory-of{
  filter: grayscale(100%);
}
body#profile .in-memory-of .thumbnail{
  margin-top: 20px;
}
.program-view-switch .btn {
    white-space: normal;
}
.program-view-switch{
	margin-bottom: 5px;
}

/*start - Temporary fix for displaying "overlapping" slots*/
.column-timeline-table .timeline-elem i{
  display: none;
}
.column-timeline-table .timeline-elem{
  background-color: #cccccc;
}
.overlapping-slots {
  margin: -3px;
}

.modal .event-title{
	padding: 3px 3px 3px 8px;
}
.modal .event-title span[data-event-star]{
	float: left;
	margin-right: 4px;
}
.modal .event-description{
	padding: 8px;
}
.flex-container{
	display: flex;
	margin-right: -5px;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.flex-container > *{
	margin-right: 5px;
	max-width: calc(50% - 10px);
  min-width: 440px;
}
.flex-2-col > * { flex: 1 0 calc(100% / 2 - 5px); }
.flex-3-col > * { flex: 1 0 calc(100% / 3 - 5px); }
.flex-4-col > * { flex: 1 0 calc(100% / 4 - 5px); }
.flex-5-col > * { flex: 1 0 calc(100% / 5 - 5px); }
.flex-6-col > * { flex: 1 0 calc(100% / 6 - 5px); }

.edit-badge textarea {
    min-height: 3em !important;
}

.imp-data{
  display:none;
}

.limit-height-250{
  max-height: 250px;
  overflow: auto;
}

.bottom-config-links{
  margin-top: 5px;
  margin-bottom: 30px;
}

.contributions-year{
  font-size: 18px;
}

.contributions-hr-year{
  margin-top: 10px;
  margin-bottom: 10px;
}

.contributions-hr-group{
  margin-top: 10px;
  margin-bottom: 10px;
}

.contributions-edition-group-1{
  margin-top: 5px;
}

.contribution-edition-group-col{
  padding-left: 0;
}

.contribution-small{
  vertical-align: middle;
}

/* Contribution timeline CSS start.
   Largely inspired by https://codepen.io/mathiesjanssen/pen/ggeBKm
*/

#contributions-timeline{
  position: relative;
}

#contributions-timeline > div:after {
  content: "";
  width: 3px;
  position: absolute;
  top: 0.5rem;
  bottom: 0rem;
  left: 60px;
  z-index: 1;
  background: #C5C5C5;
}

#contributions-timeline h3 {
  position: sticky;
  top: 2.5rem;
  color: #888;
  margin: 0;
  font-size: 1em;
  font-weight: 400;
}

#contributions-timeline div.contribution-year {
  position: relative;
}

#contributions-timeline div.contribution-year:first-child section {
  margin-top: -1.3em;
  padding-bottom: 0px;
}

#contributions-timeline div.contribution-year div {
  position: relative;
  padding-bottom: 1.25em;
  margin-bottom: 2.2em;
}

#contributions-timeline div.contribution-year div h4 {
  font-size: 0.9em;
  font-weight: bold;
  margin: 0;
  padding: 0 0 0 90px;
}

#contributions-timeline div.contribution-year div ul {
  list-style-type: none;
  padding: 0 0 0 90px;
  margin-bottom: 1em;
  font-size: 1em;
}

#contributions-timeline div.contribution-year div ul:last-child {
  margin-bottom: 0;
}

#contributions-timeline div.contribution-year div ul:first-of-type:after {
  content: "";
  width: 15px;
  height: 15px;
  background: #C5C5C5;
  border: 2px solid #FFFFFF;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  position: absolute;
  left: 54px;
  top: 3px;
  z-index: 2;
}

#contributions-timeline div.contribution-year div ul li {
  margin-left: 0.5rem;
}

#contributions-timeline div.contribution-year div ul li:not(:first-child) {
  margin-top: 0.5rem;
}

/* Contribution timeline CSS end */

.dotted {
  text-decoration-line: underline;
  text-decoration-style: dotted;
  text-decoration-thickness: 2px;
  text-decoration-skip-ink: none;
}
.social-media-widget .panel-heading svg{
	fill: #fff;
}
.dropdown.active>*>.dropdown-text-inactive, .dropdown:not(.active)>*>.dropdown-text{
  display: none;
}
.dropdown.active>*>.dropdown-text, .dropdown:not(.active)>*>.dropdown-text-inactive{
  display: unset;
}
.page-header .profile-context{
  margin-top: -10px;
}
table:not([class]) td, 
table:not([class]) th {
  padding: 2px 6px; /* Adjust the padding to create spacing inside cells */
}
/*end*/
