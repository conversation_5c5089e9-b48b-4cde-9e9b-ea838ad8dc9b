(function(factory){if (typeof define === 'function' && define.amd){define(['jquery'], factory);} else if (typeof module === 'object' && typeof module.exports === 'object'){module.exports = factory(require('jquery'));} else {factory(jQuery);}}(function(jQuery){

/*! tablesorter (FORK) - updated 2020-03-03 (v2.31.3)*/
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&"object"==typeof module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(e){return function(R){"use strict";var T=R.tablesorter={version:"2.31.3",parsers:[],widgets:[],defaults:{theme:"default",widthFixed:!1,showProcessing:!1,headerTemplate:"{content}",onRenderTemplate:null,onRenderHeader:null,cancelSelection:!0,tabIndex:!0,dateFormat:"mmddyyyy",sortMultiSortKey:"shiftKey",sortResetKey:"ctrlKey",usNumberFormat:!0,delayInit:!1,serverSideSorting:!1,resort:!0,headers:{},ignoreCase:!0,sortForce:null,sortList:[],sortAppend:null,sortStable:!1,sortInitialOrder:"asc",sortLocaleCompare:!1,sortReset:!1,sortRestart:!1,emptyTo:"bottom",stringTo:"max",duplicateSpan:!0,textExtraction:"basic",textAttribute:"data-text",textSorter:null,numberSorter:null,initWidgets:!0,widgetClass:"widget-{name}",widgets:[],widgetOptions:{zebra:["even","odd"]},initialized:null,tableClass:"",cssAsc:"",cssDesc:"",cssNone:"",cssHeader:"",cssHeaderRow:"",cssProcessing:"",cssChildRow:"tablesorter-childRow",cssInfoBlock:"tablesorter-infoOnly",cssNoSort:"tablesorter-noSort",cssIgnoreRow:"tablesorter-ignoreRow",cssIcon:"tablesorter-icon",cssIconNone:"",cssIconAsc:"",cssIconDesc:"",cssIconDisabled:"",pointerClick:"click",pointerDown:"mousedown",pointerUp:"mouseup",selectorHeaders:"> thead th, > thead td",selectorSort:"th, td",selectorRemove:".remove-me",debug:!1,headerList:[],empties:{},strings:{},parsers:[],globalize:0,imgAttr:0},css:{table:"tablesorter",cssHasChild:"tablesorter-hasChildRow",childRow:"tablesorter-childRow",colgroup:"tablesorter-colgroup",header:"tablesorter-header",headerRow:"tablesorter-headerRow",headerIn:"tablesorter-header-inner",icon:"tablesorter-icon",processing:"tablesorter-processing",sortAsc:"tablesorter-headerAsc",sortDesc:"tablesorter-headerDesc",sortNone:"tablesorter-headerUnSorted"},language:{sortAsc:"Ascending sort applied, ",sortDesc:"Descending sort applied, ",sortNone:"No sort applied, ",sortDisabled:"sorting is disabled",nextAsc:"activate to apply an ascending sort",nextDesc:"activate to apply a descending sort",nextNone:"activate to remove the sort"},regex:{templateContent:/\{content\}/g,templateIcon:/\{icon\}/g,templateName:/\{name\}/i,spaces:/\s+/g,nonWord:/\W/g,formElements:/(input|select|button|textarea)/i,chunk:/(^([+\-]?(?:\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?)?$|^0x[0-9a-f]+$|\d+)/gi,chunks:/(^\\0|\\0$)/,hex:/^0x[0-9a-f]+$/i,comma:/,/g,digitNonUS:/[\s|\.]/g,digitNegativeTest:/^\s*\([.\d]+\)/,digitNegativeReplace:/^\s*\(([.\d]+)\)/,digitTest:/^[\-+(]?\d+[)]?$/,digitReplace:/[,.'"\s]/g},string:{max:1,min:-1,emptymin:1,emptymax:-1,zero:0,none:0,"null":0,top:!0,bottom:!1},keyCodes:{enter:13},dates:{},instanceMethods:{},setup:function(t,r){if(t&&t.tHead&&0!==t.tBodies.length&&!0!==t.hasInitialized){var e,a="",s=R(t),i=R.metadata;t.hasInitialized=!1,t.isProcessing=!0,t.config=r,R.data(t,"tablesorter",r),T.debug(r,"core")&&(console[console.group?"group":"log"]("Initializing tablesorter v"+T.version),R.data(t,"startoveralltimer",new Date)),r.supportsDataObject=((e=R.fn.jquery.split("."))[0]=parseInt(e[0],10),1<e[0]||1===e[0]&&4<=parseInt(e[1],10)),r.emptyTo=r.emptyTo.toLowerCase(),r.stringTo=r.stringTo.toLowerCase(),r.last={sortList:[],clickedIndex:-1},/tablesorter\-/.test(s.attr("class"))||(a=""!==r.theme?" tablesorter-"+r.theme:""),r.namespace?r.namespace="."+r.namespace.replace(T.regex.nonWord,""):r.namespace=".tablesorter"+Math.random().toString(16).slice(2),r.table=t,r.$table=s.addClass(T.css.table+" "+r.tableClass+a+" "+r.namespace.slice(1)).attr("role","grid"),r.$headers=s.find(r.selectorHeaders),r.$table.children().children("tr").attr("role","row"),r.$tbodies=s.children("tbody:not(."+r.cssInfoBlock+")").attr({"aria-live":"polite","aria-relevant":"all"}),r.$table.children("caption").length&&((a=r.$table.children("caption")[0]).id||(a.id=r.namespace.slice(1)+"caption"),r.$table.attr("aria-labelledby",a.id)),r.widgetInit={},r.textExtraction=r.$table.attr("data-text-extraction")||r.textExtraction||"basic",T.buildHeaders(r),T.fixColumnWidth(t),T.addWidgetFromClass(t),T.applyWidgetOptions(t),T.setupParsers(r),r.totalRows=0,r.debug&&T.validateOptions(r),r.delayInit||T.buildCache(r),T.bindEvents(t,r.$headers,!0),T.bindMethods(r),r.supportsDataObject&&void 0!==s.data().sortlist?r.sortList=s.data().sortlist:i&&s.metadata()&&s.metadata().sortlist&&(r.sortList=s.metadata().sortlist),T.applyWidget(t,!0),0<r.sortList.length?(r.last.sortList=r.sortList,T.sortOn(r,r.sortList,{},!r.initWidgets)):(T.setHeadersCss(r),r.initWidgets&&T.applyWidget(t,!1)),r.showProcessing&&s.unbind("sortBegin"+r.namespace+" sortEnd"+r.namespace).bind("sortBegin"+r.namespace+" sortEnd"+r.namespace,function(e){clearTimeout(r.timerProcessing),T.isProcessing(t),"sortBegin"===e.type&&(r.timerProcessing=setTimeout(function(){T.isProcessing(t,!0)},500))}),t.hasInitialized=!0,t.isProcessing=!1,T.debug(r,"core")&&(console.log("Overall initialization time:"+T.benchmark(R.data(t,"startoveralltimer"))),T.debug(r,"core")&&console.groupEnd&&console.groupEnd()),s.triggerHandler("tablesorter-initialized",t),"function"==typeof r.initialized&&r.initialized(t)}else T.debug(r,"core")&&(t.hasInitialized?console.warn("Stopping initialization. Tablesorter has already been initialized"):console.error("Stopping initialization! No table, thead or tbody",t))},bindMethods:function(r){var e=r.$table,t=r.namespace,a="sortReset update updateRows updateAll updateHeaders addRows updateCell updateComplete sorton appendCache updateCache applyWidgetId applyWidgets refreshWidgets destroy mouseup mouseleave ".split(" ").join(t+" ");e.unbind(a.replace(T.regex.spaces," ")).bind("sortReset"+t,function(e,t){e.stopPropagation(),T.sortReset(this.config,function(e){e.isApplyingWidgets?setTimeout(function(){T.applyWidget(e,"",t)},100):T.applyWidget(e,"",t)})}).bind("updateAll"+t,function(e,t,r){e.stopPropagation(),T.updateAll(this.config,t,r)}).bind("update"+t+" updateRows"+t,function(e,t,r){e.stopPropagation(),T.update(this.config,t,r)}).bind("updateHeaders"+t,function(e,t){e.stopPropagation(),T.updateHeaders(this.config,t)}).bind("updateCell"+t,function(e,t,r,a){e.stopPropagation(),T.updateCell(this.config,t,r,a)}).bind("addRows"+t,function(e,t,r,a){e.stopPropagation(),T.addRows(this.config,t,r,a)}).bind("updateComplete"+t,function(){this.isUpdating=!1}).bind("sorton"+t,function(e,t,r,a){e.stopPropagation(),T.sortOn(this.config,t,r,a)}).bind("appendCache"+t,function(e,t,r){e.stopPropagation(),T.appendCache(this.config,r),R.isFunction(t)&&t(this)}).bind("updateCache"+t,function(e,t,r){e.stopPropagation(),T.updateCache(this.config,t,r)}).bind("applyWidgetId"+t,function(e,t){e.stopPropagation(),T.applyWidgetId(this,t)}).bind("applyWidgets"+t,function(e,t){e.stopPropagation(),T.applyWidget(this,!1,t)}).bind("refreshWidgets"+t,function(e,t,r){e.stopPropagation(),T.refreshWidgets(this,t,r)}).bind("removeWidget"+t,function(e,t,r){e.stopPropagation(),T.removeWidget(this,t,r)}).bind("destroy"+t,function(e,t,r){e.stopPropagation(),T.destroy(this,t,r)}).bind("resetToLoadState"+t,function(e){e.stopPropagation(),T.removeWidget(this,!0,!1);var t=R.extend(!0,{},r.originalSettings);(r=R.extend(!0,{},T.defaults,t)).originalSettings=t,this.hasInitialized=!1,T.setup(this,r)})},bindEvents:function(e,t,r){var a,n=(e=R(e)[0]).config,s=n.namespace,l=null;!0!==r&&(t.addClass(s.slice(1)+"_extra_headers"),(a=T.getClosest(t,"table")).length&&"TABLE"===a[0].nodeName&&a[0]!==e&&R(a[0]).addClass(s.slice(1)+"_extra_table")),a=(n.pointerDown+" "+n.pointerUp+" "+n.pointerClick+" sort keyup ").replace(T.regex.spaces," ").split(" ").join(s+" "),t.find(n.selectorSort).add(t.filter(n.selectorSort)).unbind(a).bind(a,function(e,t){var r,a,s,i=R(e.target),o=" "+e.type+" ";if(!(1!==(e.which||e.button)&&!o.match(" "+n.pointerClick+" | sort | keyup ")||" keyup "===o&&e.which!==T.keyCodes.enter||o.match(" "+n.pointerClick+" ")&&void 0!==e.which||o.match(" "+n.pointerUp+" ")&&l!==e.target&&!0!==t)){if(o.match(" "+n.pointerDown+" "))return l=e.target,void("1"===(s=i.jquery.split("."))[0]&&s[1]<4&&e.preventDefault());if(l=null,r=T.getClosest(R(this),"."+T.css.header),T.regex.formElements.test(e.target.nodeName)||i.hasClass(n.cssNoSort)||0<i.parents("."+n.cssNoSort).length||r.hasClass("sorter-false")||0<i.parents("button").length)return!n.cancelSelection;n.delayInit&&T.isEmptyObject(n.cache)&&T.buildCache(n),n.last.clickedIndex=r.attr("data-column")||r.index(),(a=n.$headerIndexed[n.last.clickedIndex][0])&&!a.sortDisabled&&T.initSort(n,a,e)}}),n.cancelSelection&&t.attr("unselectable","on").bind("selectstart",!1).css({"user-select":"none",MozUserSelect:"none"})},buildHeaders:function(l){var e,c,t,r;for(l.headerList=[],l.headerContent=[],l.sortVars=[],T.debug(l,"core")&&(t=new Date),l.columns=T.computeColumnIndex(l.$table.children("thead, tfoot").children("tr")),c=l.cssIcon?'<i class="'+(l.cssIcon===T.css.icon?T.css.icon:l.cssIcon+" "+T.css.icon)+'"></i>':"",l.$headers=R(R.map(l.$table.find(l.selectorHeaders),function(e,t){var r,a,s,i,o,n=R(e);if(!T.getClosest(n,"tr").hasClass(l.cssIgnoreRow))return/(th|td)/i.test(e.nodeName)||(o=T.getClosest(n,"th, td"),n.attr("data-column",o.attr("data-column"))),r=T.getColumnData(l.table,l.headers,t,!0),l.headerContent[t]=n.html(),""===l.headerTemplate||n.find("."+T.css.headerIn).length||(i=l.headerTemplate.replace(T.regex.templateContent,n.html()).replace(T.regex.templateIcon,n.find("."+T.css.icon).length?"":c),l.onRenderTemplate&&(a=l.onRenderTemplate.apply(n,[t,i]))&&"string"==typeof a&&(i=a),n.html('<div class="'+T.css.headerIn+'">'+i+"</div>")),l.onRenderHeader&&l.onRenderHeader.apply(n,[t,l,l.$table]),s=parseInt(n.attr("data-column"),10),e.column=s,o=T.getOrder(T.getData(n,r,"sortInitialOrder")||l.sortInitialOrder),l.sortVars[s]={count:-1,order:o?l.sortReset?[1,0,2]:[1,0]:l.sortReset?[0,1,2]:[0,1],lockedOrder:!1,sortedBy:""},void 0!==(o=T.getData(n,r,"lockedOrder")||!1)&&!1!==o&&(l.sortVars[s].lockedOrder=!0,l.sortVars[s].order=T.getOrder(o)?[1,1]:[0,0]),l.headerList[t]=e,n.addClass(T.css.header+" "+l.cssHeader),T.getClosest(n,"tr").addClass(T.css.headerRow+" "+l.cssHeaderRow).attr("role","row"),l.tabIndex&&n.attr("tabindex",0),e})),l.$headerIndexed=[],r=0;r<l.columns;r++)T.isEmptyObject(l.sortVars[r])&&(l.sortVars[r]={}),e=l.$headers.filter('[data-column="'+r+'"]'),l.$headerIndexed[r]=e.length?e.not(".sorter-false").length?e.not(".sorter-false").filter(":last"):e.filter(":last"):R();l.$table.find(l.selectorHeaders).attr({scope:"col",role:"columnheader"}),T.updateHeader(l),T.debug(l,"core")&&(console.log("Built headers:"+T.benchmark(t)),console.log(l.$headers))},addInstanceMethods:function(e){R.extend(T.instanceMethods,e)},setupParsers:function(e,t){var r,a,s,i,o,n,l,c,d,f,u,g,p,h,m=e.table,b=0,y=T.debug(e,"core"),v={};if(e.$tbodies=e.$table.children("tbody:not(."+e.cssInfoBlock+")"),0===(h=(p=void 0===t?e.$tbodies:t).length))return y?console.warn("Warning: *Empty table!* Not building a parser cache"):"";for(y&&(g=new Date,console[console.group?"group":"log"]("Detecting parsers for each column")),a={extractors:[],parsers:[]};b<h;){if((r=p[b].rows).length)for(o=0,i=e.columns,n=0;n<i;n++){if((l=e.$headerIndexed[o])&&l.length&&(c=T.getColumnData(m,e.headers,o),u=T.getParserById(T.getData(l,c,"extractor")),f=T.getParserById(T.getData(l,c,"sorter")),d="false"===T.getData(l,c,"parser"),e.empties[o]=(T.getData(l,c,"empty")||e.emptyTo||(e.emptyToBottom?"bottom":"top")).toLowerCase(),e.strings[o]=(T.getData(l,c,"string")||e.stringTo||"max").toLowerCase(),d&&(f=T.getParserById("no-parser")),u=u||!1,f=f||T.detectParserForColumn(e,r,-1,o),y&&(v["("+o+") "+l.text()]={parser:f.id,extractor:u?u.id:"none",string:e.strings[o],empty:e.empties[o]}),a.parsers[o]=f,a.extractors[o]=u,0<(s=l[0].colSpan-1)))for(o+=s,i+=s;0<s+1;)a.parsers[o-s]=f,a.extractors[o-s]=u,s--;o++}b+=a.parsers.length?h:1}y&&(T.isEmptyObject(v)?console.warn("  No parsers detected!"):console[console.table?"table":"log"](v),console.log("Completed detecting parsers"+T.benchmark(g)),console.groupEnd&&console.groupEnd()),e.parsers=a.parsers,e.extractors=a.extractors},addParser:function(e){var t,r=T.parsers.length,a=!0;for(t=0;t<r;t++)T.parsers[t].id.toLowerCase()===e.id.toLowerCase()&&(a=!1);a&&(T.parsers[T.parsers.length]=e)},getParserById:function(e){if("false"==e)return!1;var t,r=T.parsers.length;for(t=0;t<r;t++)if(T.parsers[t].id.toLowerCase()===e.toString().toLowerCase())return T.parsers[t];return!1},detectParserForColumn:function(e,t,r,a){for(var s,i,o,n=T.parsers.length,l=!1,c="",d=T.debug(e,"core"),f=!0;""===c&&f;)(o=t[++r])&&r<50?o.className.indexOf(T.cssIgnoreRow)<0&&(l=t[r].cells[a],c=T.getElementText(e,l,a),i=R(l),d&&console.log("Checking if value was empty on row "+r+", column: "+a+': "'+c+'"')):f=!1;for(;0<=--n;)if((s=T.parsers[n])&&"text"!==s.id&&s.is&&s.is(c,e.table,l,i))return s;return T.getParserById("text")},getElementText:function(e,t,r){if(!t)return"";var a,s=e.textExtraction||"",i=t.jquery?t:R(t);return"string"==typeof s?"basic"===s&&void 0!==(a=i.attr(e.textAttribute))?R.trim(a):R.trim(t.textContent||i.text()):"function"==typeof s?R.trim(s(i[0],e.table,r)):"function"==typeof(a=T.getColumnData(e.table,s,r))?R.trim(a(i[0],e.table,r)):R.trim(i[0].textContent||i.text())},getParsedText:function(e,t,r,a){void 0===a&&(a=T.getElementText(e,t,r));var s=""+a,i=e.parsers[r],o=e.extractors[r];return i&&(o&&"function"==typeof o.format&&(a=o.format(a,e.table,t,r)),s="no-parser"===i.id?"":i.format(""+a,e.table,t,r),e.ignoreCase&&"string"==typeof s&&(s=s.toLowerCase())),s},buildCache:function(e,t,r){var a,s,i,o,n,l,c,d,f,u,g,p,h,m,b,y,v,w,x,C,_,$,S=e.table,z=e.parsers,F=T.debug(e,"core");if(e.$tbodies=e.$table.children("tbody:not(."+e.cssInfoBlock+")"),c=void 0===r?e.$tbodies:r,e.cache={},e.totalRows=0,!z)return F?console.warn("Warning: *Empty table!* Not building a cache"):"";for(F&&(p=new Date),e.showProcessing&&T.isProcessing(S,!0),l=0;l<c.length;l++){for(y=[],a=e.cache[l]={normalized:[]},h=c[l]&&c[l].rows.length||0,o=0;o<h;++o)if(m={child:[],raw:[]},f=[],!(d=R(c[l].rows[o])).hasClass(e.selectorRemove.slice(1)))if(d.hasClass(e.cssChildRow)&&0!==o)for(_=a.normalized.length-1,(b=a.normalized[_][e.columns]).$row=b.$row.add(d),d.prev().hasClass(e.cssChildRow)||d.prev().addClass(T.css.cssHasChild),u=d.children("th, td"),_=b.child.length,b.child[_]=[],w=0,C=e.columns,n=0;n<C;n++)(g=u[n])&&(b.child[_][n]=T.getParsedText(e,g,n),0<(v=u[n].colSpan-1)&&(w+=v,C+=v)),w++;else{for(m.$row=d,m.order=o,w=0,C=e.columns,n=0;n<C;++n){if((g=d[0].cells[n])&&w<e.columns&&(!(x=void 0!==z[w])&&F&&console.warn("No parser found for row: "+o+", column: "+n+'; cell containing: "'+R(g).text()+'"; does it have a header?'),s=T.getElementText(e,g,w),m.raw[w]=s,i=T.getParsedText(e,g,w,s),f[w]=i,x&&"numeric"===(z[w].type||"").toLowerCase()&&(y[w]=Math.max(Math.abs(i)||0,y[w]||0)),0<(v=g.colSpan-1))){for($=0;$<=v;)i=e.duplicateSpan||0===$?i:"string"!=typeof e.textExtraction&&T.getElementText(e,g,w+$)||"",m.raw[w+$]=i,f[w+$]=i,$++;w+=v,C+=v}w++}f[e.columns]=m,a.normalized[a.normalized.length]=f}a.colMax=y,e.totalRows+=a.normalized.length}if(e.showProcessing&&T.isProcessing(S),F){for(_=Math.min(5,e.cache[0].normalized.length),console[console.group?"group":"log"]("Building cache for "+e.totalRows+" rows (showing "+_+" rows in log) and "+e.columns+" columns"+T.benchmark(p)),s={},n=0;n<e.columns;n++)for(w=0;w<_;w++)s["row: "+w]||(s["row: "+w]={}),s["row: "+w][e.$headerIndexed[n].text()]=e.cache[0].normalized[w][n];console[console.table?"table":"log"](s),console.groupEnd&&console.groupEnd()}R.isFunction(t)&&t(S)},getColumnText:function(e,t,r,a){var s,i,o,n,l,c,d,f,u,g,p="function"==typeof r,h="all"===t,m={raw:[],parsed:[],$cell:[]},b=(e=R(e)[0]).config;if(!T.isEmptyObject(b)){for(l=b.$tbodies.length,s=0;s<l;s++)for(c=(o=b.cache[s].normalized).length,i=0;i<c;i++)n=o[i],a&&!n[b.columns].$row.is(a)||(g=!0,f=h?n.slice(0,b.columns):n[t],n=n[b.columns],d=h?n.raw:n.raw[t],u=h?n.$row.children():n.$row.children().eq(t),p&&(g=r({tbodyIndex:s,rowIndex:i,parsed:f,raw:d,$row:n.$row,$cell:u})),!1!==g&&(m.parsed[m.parsed.length]=f,m.raw[m.raw.length]=d,m.$cell[m.$cell.length]=u));return m}T.debug(b,"core")&&console.warn("No cache found - aborting getColumnText function!")},setHeadersCss:function(i){function e(e,t){e.removeClass(o).addClass(n[t]).attr("aria-sort",c[t]).find("."+T.css.icon).removeClass(l[2]).addClass(l[t])}var t,r,a=i.sortList,s=a.length,o=T.css.sortNone+" "+i.cssNone,n=[T.css.sortAsc+" "+i.cssAsc,T.css.sortDesc+" "+i.cssDesc],l=[i.cssIconAsc,i.cssIconDesc,i.cssIconNone],c=["ascending","descending"],d=i.$table.find("tfoot tr").children("td, th").add(R(i.namespace+"_extra_headers")).removeClass(n.join(" ")),f=i.$headers.add(R("thead "+i.namespace+"_extra_headers")).removeClass(n.join(" ")).addClass(o).attr("aria-sort","none").find("."+T.css.icon).removeClass(l.join(" ")).end();for(f.not(".sorter-false").find("."+T.css.icon).addClass(l[2]),i.cssIconDisabled&&f.filter(".sorter-false").find("."+T.css.icon).addClass(i.cssIconDisabled),t=0;t<s;t++)if(2!==a[t][1]){if((f=(f=i.$headers.filter(function(e){for(var t=!0,r=i.$headers.eq(e),a=parseInt(r.attr("data-column"),10),s=a+T.getClosest(r,"th, td")[0].colSpan;a<s;a++)t=!!t&&(t||-1<T.isValueInArray(a,i.sortList));return t})).not(".sorter-false").filter('[data-column="'+a[t][0]+'"]'+(1===s?":last":""))).length)for(r=0;r<f.length;r++)f[r].sortDisabled||e(f.eq(r),a[t][1]);d.length&&e(d.filter('[data-column="'+a[t][0]+'"]'),a[t][1])}for(s=i.$headers.length,t=0;t<s;t++)T.setColumnAriaLabel(i,i.$headers.eq(t))},getClosest:function(e,t){return R.fn.closest?e.closest(t):e.is(t)?e:e.parents(t).filter(":first")},setColumnAriaLabel:function(e,t,r){if(t.length){var a=parseInt(t.attr("data-column"),10),s=e.sortVars[a],i=t.hasClass(T.css.sortAsc)?"sortAsc":t.hasClass(T.css.sortDesc)?"sortDesc":"sortNone",o=R.trim(t.text())+": "+T.language[i];t.hasClass("sorter-false")||!1===r?o+=T.language.sortDisabled:(i=(s.count+1)%s.order.length,r=s.order[i],o+=T.language[0===r?"nextAsc":1===r?"nextDesc":"nextNone"]),t.attr("aria-label",o),s.sortedBy?t.attr("data-sortedBy",s.sortedBy):t.removeAttr("data-sortedBy")}},updateHeader:function(e){var t,r,a,s,i=e.table,o=e.$headers.length;for(t=0;t<o;t++)a=e.$headers.eq(t),s=T.getColumnData(i,e.headers,t,!0),r="false"===T.getData(a,s,"sorter")||"false"===T.getData(a,s,"parser"),T.setColumnSort(e,a,r)},setColumnSort:function(e,t,r){var a=e.table.id;t[0].sortDisabled=r,t[r?"addClass":"removeClass"]("sorter-false").attr("aria-disabled",""+r),e.tabIndex&&(r?t.removeAttr("tabindex"):t.attr("tabindex","0")),a&&(r?t.removeAttr("aria-controls"):t.attr("aria-controls",a))},updateHeaderSortCount:function(e,t){var r,a,s,i,o,n,l,c,d=t||e.sortList,f=d.length;for(e.sortList=[],i=0;i<f;i++)if(l=d[i],(r=parseInt(l[0],10))<e.columns){switch(e.sortVars[r].order||(c=T.getOrder(e.sortInitialOrder)?e.sortReset?[1,0,2]:[1,0]:e.sortReset?[0,1,2]:[0,1],e.sortVars[r].order=c,e.sortVars[r].count=0),c=e.sortVars[r].order,a=(a=(""+l[1]).match(/^(1|d|s|o|n)/))?a[0]:""){case"1":case"d":a=1;break;case"s":a=o||0;break;case"o":a=0===(n=c[(o||0)%c.length])?1:1===n?0:2;break;case"n":a=c[++e.sortVars[r].count%c.length];break;default:a=0}o=0===i?a:o,s=[r,parseInt(a,10)||0],e.sortList[e.sortList.length]=s,a=R.inArray(s[1],c),e.sortVars[r].count=0<=a?a:s[1]%c.length}},updateAll:function(e,t,r){var a=e.table;a.isUpdating=!0,T.refreshWidgets(a,!0,!0),T.buildHeaders(e),T.bindEvents(a,e.$headers,!0),T.bindMethods(e),T.commonUpdate(e,t,r)},update:function(e,t,r){e.table.isUpdating=!0,T.updateHeader(e),T.commonUpdate(e,t,r)},updateHeaders:function(e,t){e.table.isUpdating=!0,T.buildHeaders(e),T.bindEvents(e.table,e.$headers,!0),T.resortComplete(e,t)},updateCell:function(e,t,r,a){if(R(t).closest("tr").hasClass(e.cssChildRow))console.warn('Tablesorter Warning! "updateCell" for child row content has been disabled, use "update" instead');else{if(T.isEmptyObject(e.cache))return T.updateHeader(e),void T.commonUpdate(e,r,a);e.table.isUpdating=!0,e.$table.find(e.selectorRemove).remove();var s,i,o,n,l,c,d=e.$tbodies,f=R(t),u=d.index(T.getClosest(f,"tbody")),g=e.cache[u],p=T.getClosest(f,"tr");if(t=f[0],d.length&&0<=u){if(o=d.eq(u).find("tr").not("."+e.cssChildRow).index(p),l=g.normalized[o],(c=p[0].cells.length)!==e.columns)for(s=!1,i=n=0;i<c;i++)s||p[0].cells[i]===t?s=!0:n+=p[0].cells[i].colSpan;else n=f.index();s=T.getElementText(e,t,n),l[e.columns].raw[n]=s,s=T.getParsedText(e,t,n,s),l[n]=s,"numeric"===(e.parsers[n].type||"").toLowerCase()&&(g.colMax[n]=Math.max(Math.abs(s)||0,g.colMax[n]||0)),!1!==(s="undefined"!==r?r:e.resort)?T.checkResort(e,s,a):T.resortComplete(e,a)}else T.debug(e,"core")&&console.error("updateCell aborted, tbody missing or not within the indicated table"),e.table.isUpdating=!1}},addRows:function(e,t,r,a){var s,i,o,n,l,c,d,f,u,g,p,h,m,b="string"==typeof t&&1===e.$tbodies.length&&/<tr/.test(t||""),y=e.table;if(b)t=R(t),e.$tbodies.append(t);else if(!(t&&t instanceof R&&T.getClosest(t,"table")[0]===e.table))return T.debug(e,"core")&&console.error("addRows method requires (1) a jQuery selector reference to rows that have already been added to the table, or (2) row HTML string to be added to a table with only one tbody"),!1;if(y.isUpdating=!0,T.isEmptyObject(e.cache))T.updateHeader(e),T.commonUpdate(e,r,a);else{for(l=t.filter("tr").attr("role","row").length,o=e.$tbodies.index(t.parents("tbody").filter(":first")),e.parsers&&e.parsers.length||T.setupParsers(e),n=0;n<l;n++){for(u=0,d=t[n].cells.length,f=e.cache[o].normalized.length,p=[],g={child:[],raw:[],$row:t.eq(n),order:f},c=0;c<d;c++)h=t[n].cells[c],s=T.getElementText(e,h,u),g.raw[u]=s,i=T.getParsedText(e,h,u,s),p[u]=i,"numeric"===(e.parsers[u].type||"").toLowerCase()&&(e.cache[o].colMax[u]=Math.max(Math.abs(i)||0,e.cache[o].colMax[u]||0)),0<(m=h.colSpan-1)&&(u+=m),u++;p[e.columns]=g,e.cache[o].normalized[f]=p}T.checkResort(e,r,a)}},updateCache:function(e,t,r){e.parsers&&e.parsers.length||T.setupParsers(e,r),T.buildCache(e,t,r)},appendCache:function(e,t){var r,a,s,i,o,n,l,c=e.table,d=e.$tbodies,f=[],u=e.cache;if(T.isEmptyObject(u))return e.appender?e.appender(c,f):c.isUpdating?e.$table.triggerHandler("updateComplete",c):"";for(T.debug(e,"core")&&(l=new Date),n=0;n<d.length;n++)if((s=d.eq(n)).length){for(i=T.processTbody(c,s,!0),a=(r=u[n].normalized).length,o=0;o<a;o++)f[f.length]=r[o][e.columns].$row,e.appender&&(!e.pager||e.pager.removeRows||e.pager.ajax)||i.append(r[o][e.columns].$row);T.processTbody(c,i,!1)}e.appender&&e.appender(c,f),T.debug(e,"core")&&console.log("Rebuilt table"+T.benchmark(l)),t||e.appender||T.applyWidget(c),c.isUpdating&&e.$table.triggerHandler("updateComplete",c)},commonUpdate:function(e,t,r){e.$table.find(e.selectorRemove).remove(),T.setupParsers(e),T.buildCache(e),T.checkResort(e,t,r)},initSort:function(t,e,r){if(t.table.isUpdating)return setTimeout(function(){T.initSort(t,e,r)},50);var a,s,i,o,n,l,c,d=!r[t.sortMultiSortKey],f=t.table,u=t.$headers.length,g=T.getClosest(R(e),"th, td"),p=parseInt(g.attr("data-column"),10),h="mouseup"===r.type?"user":r.type,m=t.sortVars[p].order;if(g=g[0],t.$table.triggerHandler("sortStart",f),l=(t.sortVars[p].count+1)%m.length,t.sortVars[p].count=r[t.sortResetKey]?2:l,t.sortRestart)for(i=0;i<u;i++)c=t.$headers.eq(i),p!==(l=parseInt(c.attr("data-column"),10))&&(d||c.hasClass(T.css.sortNone))&&(t.sortVars[l].count=-1);if(d){if(R.each(t.sortVars,function(e){t.sortVars[e].sortedBy=""}),t.sortList=[],t.last.sortList=[],null!==t.sortForce)for(a=t.sortForce,s=0;s<a.length;s++)a[s][0]!==p&&(t.sortList[t.sortList.length]=a[s],t.sortVars[a[s][0]].sortedBy="sortForce");if((o=m[t.sortVars[p].count])<2&&(t.sortList[t.sortList.length]=[p,o],t.sortVars[p].sortedBy=h,1<g.colSpan))for(s=1;s<g.colSpan;s++)t.sortList[t.sortList.length]=[p+s,o],t.sortVars[p+s].count=R.inArray(o,m),t.sortVars[p+s].sortedBy=h}else if(t.sortList=R.extend([],t.last.sortList),0<=T.isValueInArray(p,t.sortList))for(t.sortVars[p].sortedBy=h,s=0;s<t.sortList.length;s++)(l=t.sortList[s])[0]===p&&(l[1]=m[t.sortVars[p].count],2===l[1]&&(t.sortList.splice(s,1),t.sortVars[p].count=-1));else if(o=m[t.sortVars[p].count],t.sortVars[p].sortedBy=h,o<2&&(t.sortList[t.sortList.length]=[p,o],1<g.colSpan))for(s=1;s<g.colSpan;s++)t.sortList[t.sortList.length]=[p+s,o],t.sortVars[p+s].count=R.inArray(o,m),t.sortVars[p+s].sortedBy=h;if(t.last.sortList=R.extend([],t.sortList),t.sortList.length&&t.sortAppend&&(a=R.isArray(t.sortAppend)?t.sortAppend:t.sortAppend[t.sortList[0][0]],!T.isEmptyObject(a)))for(s=0;s<a.length;s++)if(a[s][0]!==p&&T.isValueInArray(a[s][0],t.sortList)<0){if(n=(""+(o=a[s][1])).match(/^(a|d|s|o|n)/))switch(l=t.sortList[0][1],n[0]){case"d":o=1;break;case"s":o=l;break;case"o":o=0===l?1:0;break;case"n":o=(l+1)%m.length;break;default:o=0}t.sortList[t.sortList.length]=[a[s][0],o],t.sortVars[a[s][0]].sortedBy="sortAppend"}t.$table.triggerHandler("sortBegin",f),setTimeout(function(){T.setHeadersCss(t),T.multisort(t),T.appendCache(t),t.$table.triggerHandler("sortBeforeEnd",f),t.$table.triggerHandler("sortEnd",f)},1)},multisort:function(c){var e,t,d,r,f=c.table,u=[],g=0,p=c.textSorter||"",h=c.sortList,m=h.length,a=c.$tbodies.length;if(!c.serverSideSorting&&!T.isEmptyObject(c.cache)){if(T.debug(c,"core")&&(t=new Date),"object"==typeof p)for(d=c.columns;d--;)"function"==typeof(r=T.getColumnData(f,p,d))&&(u[d]=r);for(e=0;e<a;e++)d=c.cache[e].colMax,c.cache[e].normalized.sort(function(e,t){var r,a,s,i,o,n,l;for(r=0;r<m;r++){if(s=h[r][0],i=h[r][1],g=0===i,c.sortStable&&e[s]===t[s]&&1===m)return e[c.columns].order-t[c.columns].order;if(o=(a=/n/i.test(T.getSortType(c.parsers,s)))&&c.strings[s]?(a="boolean"==typeof T.string[c.strings[s]]?(g?1:-1)*(T.string[c.strings[s]]?-1:1):c.strings[s]&&T.string[c.strings[s]]||0,c.numberSorter?c.numberSorter(e[s],t[s],g,d[s],f):T["sortNumeric"+(g?"Asc":"Desc")](e[s],t[s],a,d[s],s,c)):(n=g?e:t,l=g?t:e,"function"==typeof p?p(n[s],l[s],g,s,f):"function"==typeof u[s]?u[s](n[s],l[s],g,s,f):T["sortNatural"+(g?"Asc":"Desc")](e[s]||"",t[s]||"",s,c)))return o}return e[c.columns].order-t[c.columns].order});T.debug(c,"core")&&console.log("Applying sort "+h.toString()+T.benchmark(t))}},resortComplete:function(e,t){e.table.isUpdating&&e.$table.triggerHandler("updateComplete",e.table),R.isFunction(t)&&t(e.table)},checkResort:function(e,t,r){var a=R.isArray(t)?t:e.sortList;!1===(void 0===t?e.resort:t)||e.serverSideSorting||e.table.isProcessing?(T.resortComplete(e,r),T.applyWidget(e.table,!1)):a.length?T.sortOn(e,a,function(){T.resortComplete(e,r)},!0):T.sortReset(e,function(){T.resortComplete(e,r),T.applyWidget(e.table,!1)})},sortOn:function(e,t,r,a){var s,i=e.table;for(e.$table.triggerHandler("sortStart",i),s=0;s<e.columns;s++)e.sortVars[s].sortedBy=-1<T.isValueInArray(s,t)?"sorton":"";T.updateHeaderSortCount(e,t),T.setHeadersCss(e),e.delayInit&&T.isEmptyObject(e.cache)&&T.buildCache(e),e.$table.triggerHandler("sortBegin",i),T.multisort(e),T.appendCache(e,a),e.$table.triggerHandler("sortBeforeEnd",i),e.$table.triggerHandler("sortEnd",i),T.applyWidget(i),R.isFunction(r)&&r(i)},sortReset:function(e,t){var r;for(e.sortList=[],r=0;r<e.columns;r++)e.sortVars[r].count=-1,e.sortVars[r].sortedBy="";T.setHeadersCss(e),T.multisort(e),T.appendCache(e),R.isFunction(t)&&t(e.table)},getSortType:function(e,t){return e&&e[t]&&e[t].type||""},getOrder:function(e){return/^d/i.test(e)||1===e},sortNatural:function(e,t){if(e===t)return 0;e=(e||"").toString(),t=(t||"").toString();var r,a,s,i,o,n,l=T.regex;if(l.hex.test(t)){if((r=parseInt(e.match(l.hex),16))<(a=parseInt(t.match(l.hex),16)))return-1;if(a<r)return 1}for(r=e.replace(l.chunk,"\\0$1\\0").replace(l.chunks,"").split("\\0"),a=t.replace(l.chunk,"\\0$1\\0").replace(l.chunks,"").split("\\0"),n=Math.max(r.length,a.length),o=0;o<n;o++){if(s=isNaN(r[o])?r[o]||0:parseFloat(r[o])||0,i=isNaN(a[o])?a[o]||0:parseFloat(a[o])||0,isNaN(s)!==isNaN(i))return isNaN(s)?1:-1;if(typeof s!=typeof i&&(s+="",i+=""),s<i)return-1;if(i<s)return 1}return 0},sortNaturalAsc:function(e,t,r,a){if(e===t)return 0;var s=T.string[a.empties[r]||a.emptyTo];return""===e&&0!==s?"boolean"==typeof s?s?-1:1:-s||-1:""===t&&0!==s?"boolean"==typeof s?s?1:-1:s||1:T.sortNatural(e,t)},sortNaturalDesc:function(e,t,r,a){if(e===t)return 0;var s=T.string[a.empties[r]||a.emptyTo];return""===e&&0!==s?"boolean"==typeof s?s?-1:1:s||1:""===t&&0!==s?"boolean"==typeof s?s?1:-1:-s||-1:T.sortNatural(t,e)},sortText:function(e,t){return t<e?1:e<t?-1:0},getTextValue:function(e,t,r){if(r){var a,s=e?e.length:0,i=r+t;for(a=0;a<s;a++)i+=e.charCodeAt(a);return t*i}return 0},sortNumericAsc:function(e,t,r,a,s,i){if(e===t)return 0;var o=T.string[i.empties[s]||i.emptyTo];return""===e&&0!==o?"boolean"==typeof o?o?-1:1:-o||-1:""===t&&0!==o?"boolean"==typeof o?o?1:-1:o||1:(isNaN(e)&&(e=T.getTextValue(e,r,a)),isNaN(t)&&(t=T.getTextValue(t,r,a)),e-t)},sortNumericDesc:function(e,t,r,a,s,i){if(e===t)return 0;var o=T.string[i.empties[s]||i.emptyTo];return""===e&&0!==o?"boolean"==typeof o?o?-1:1:o||1:""===t&&0!==o?"boolean"==typeof o?o?1:-1:-o||-1:(isNaN(e)&&(e=T.getTextValue(e,r,a)),isNaN(t)&&(t=T.getTextValue(t,r,a)),t-e)},sortNumeric:function(e,t){return e-t},addWidget:function(e){e.id&&!T.isEmptyObject(T.getWidgetById(e.id))&&console.warn('"'+e.id+'" widget was loaded more than once!'),T.widgets[T.widgets.length]=e},hasWidget:function(e,t){return(e=R(e)).length&&e[0].config&&e[0].config.widgetInit[t]||!1},getWidgetById:function(e){var t,r,a=T.widgets.length;for(t=0;t<a;t++)if((r=T.widgets[t])&&r.id&&r.id.toLowerCase()===e.toLowerCase())return r},applyWidgetOptions:function(e){var t,r,a,s=e.config,i=s.widgets.length;if(i)for(t=0;t<i;t++)(r=T.getWidgetById(s.widgets[t]))&&r.options&&(a=R.extend(!0,{},r.options),s.widgetOptions=R.extend(!0,a,s.widgetOptions),R.extend(!0,T.defaults.widgetOptions,r.options))},addWidgetFromClass:function(e){var t,r,a=e.config,s="^"+a.widgetClass.replace(T.regex.templateName,"(\\S+)+")+"$",i=new RegExp(s,"g"),o=(e.className||"").split(T.regex.spaces);if(o.length)for(t=o.length,r=0;r<t;r++)o[r].match(i)&&(a.widgets[a.widgets.length]=o[r].replace(i,"$1"))},applyWidgetId:function(e,t,r){var a,s,i,o=(e=R(e)[0]).config,n=o.widgetOptions,l=T.debug(o,"core"),c=T.getWidgetById(t);c&&(i=c.id,a=!1,R.inArray(i,o.widgets)<0&&(o.widgets[o.widgets.length]=i),l&&(s=new Date),!r&&o.widgetInit[i]||(o.widgetInit[i]=!0,e.hasInitialized&&T.applyWidgetOptions(e),"function"==typeof c.init&&(a=!0,l&&console[console.group?"group":"log"]("Initializing "+i+" widget"),c.init(e,c,o,n))),r||"function"!=typeof c.format||(a=!0,l&&console[console.group?"group":"log"]("Updating "+i+" widget"),c.format(e,o,n,!1)),l&&a&&(console.log("Completed "+(r?"initializing ":"applying ")+i+" widget"+T.benchmark(s)),console.groupEnd&&console.groupEnd()))},applyWidget:function(e,t,r){var a,s,i,o,n,l=(e=R(e)[0]).config,c=T.debug(l,"core"),d=[];if(!1===t||!e.hasInitialized||!e.isApplyingWidgets&&!e.isUpdating){if(c&&(n=new Date),T.addWidgetFromClass(e),clearTimeout(l.timerReady),l.widgets.length){for(e.isApplyingWidgets=!0,l.widgets=R.grep(l.widgets,function(e,t){return R.inArray(e,l.widgets)===t}),s=(i=l.widgets||[]).length,a=0;a<s;a++)(o=T.getWidgetById(i[a]))&&o.id?(o.priority||(o.priority=10),d[a]=o):c&&console.warn('"'+i[a]+'" was enabled, but the widget code has not been loaded!');for(d.sort(function(e,t){return e.priority<t.priority?-1:e.priority===t.priority?0:1}),s=d.length,c&&console[console.group?"group":"log"]("Start "+(t?"initializing":"applying")+" widgets"),a=0;a<s;a++)(o=d[a])&&o.id&&T.applyWidgetId(e,o.id,t);c&&console.groupEnd&&console.groupEnd()}l.timerReady=setTimeout(function(){e.isApplyingWidgets=!1,R.data(e,"lastWidgetApplication",new Date),l.$table.triggerHandler("tablesorter-ready"),t||"function"!=typeof r||r(e),c&&(o=l.widgets.length,console.log("Completed "+(!0===t?"initializing ":"applying ")+o+" widget"+(1!==o?"s":"")+T.benchmark(n)))},10)}},removeWidget:function(e,t,r){var a,s,i,o,n=(e=R(e)[0]).config;if(!0===t)for(t=[],o=T.widgets.length,i=0;i<o;i++)(s=T.widgets[i])&&s.id&&(t[t.length]=s.id);else t=(R.isArray(t)?t.join(","):t||"").toLowerCase().split(/[\s,]+/);for(o=t.length,a=0;a<o;a++)s=T.getWidgetById(t[a]),0<=(i=R.inArray(t[a],n.widgets))&&!0!==r&&n.widgets.splice(i,1),s&&s.remove&&(T.debug(n,"core")&&console.log((r?"Refreshing":"Removing")+' "'+t[a]+'" widget'),s.remove(e,n,n.widgetOptions,r),n.widgetInit[t[a]]=!1);n.$table.triggerHandler("widgetRemoveEnd",e)},refreshWidgets:function(e,t,r){function a(e){R(e).triggerHandler("refreshComplete")}var s,i,o=(e=R(e)[0]).config.widgets,n=T.widgets,l=n.length,c=[];for(s=0;s<l;s++)(i=n[s])&&i.id&&(t||R.inArray(i.id,o)<0)&&(c[c.length]=i.id);T.removeWidget(e,c.join(","),!0),!0!==r?(T.applyWidget(e,t||!1,a),t&&T.applyWidget(e,!1,a)):a(e)},benchmark:function(e){return" ("+((new Date).getTime()-e.getTime())+" ms)"},log:function(){console.log(arguments)},debug:function(e,t){return e&&(!0===e.debug||"string"==typeof e.debug&&-1<e.debug.indexOf(t))},isEmptyObject:function(e){for(var t in e)return!1;return!0},isValueInArray:function(e,t){var r,a=t&&t.length||0;for(r=0;r<a;r++)if(t[r][0]===e)return r;return-1},formatFloat:function(e,t){return"string"!=typeof e||""===e?e:(e=(t&&t.config?!1!==t.config.usNumberFormat:void 0===t||t)?e.replace(T.regex.comma,""):e.replace(T.regex.digitNonUS,"").replace(T.regex.comma,"."),T.regex.digitNegativeTest.test(e)&&(e=e.replace(T.regex.digitNegativeReplace,"-$1")),r=parseFloat(e),isNaN(r)?R.trim(e):r);var r},isDigit:function(e){return isNaN(e)?T.regex.digitTest.test(e.toString().replace(T.regex.digitReplace,"")):""!==e},computeColumnIndex:function(e,t){var r,a,s,i,o,n,l,c,d,f,u=t&&t.columns||0,g=[],p=new Array(u);for(r=0;r<e.length;r++)for(n=e[r].cells,a=0;a<n.length;a++){for(l=r,c=(o=n[a]).rowSpan||1,d=o.colSpan||1,void 0===g[l]&&(g[l]=[]),s=0;s<g[l].length+1;s++)if(void 0===g[l][s]){f=s;break}for(u&&o.cellIndex===f||(o.setAttribute?o.setAttribute("data-column",f):R(o).attr("data-column",f)),s=l;s<l+c;s++)for(void 0===g[s]&&(g[s]=[]),p=g[s],i=f;i<f+d;i++)p[i]="x"}return T.checkColumnCount(e,g,p.length),p.length},checkColumnCount:function(e,t,r){var a,s,i=!0,o=[];for(a=0;a<t.length;a++)if(t[a]&&(s=t[a].length,t[a].length!==r)){i=!1;break}i||(e.each(function(e,t){var r=t.parentElement.nodeName;o.indexOf(r)<0&&o.push(r)}),console.error("Invalid or incorrect number of columns in the "+o.join(" or ")+"; expected "+r+", but found "+s+" columns"))},fixColumnWidth:function(e){var t,r,a,s,i,o=(e=R(e)[0]).config,n=o.$table.children("colgroup");if(n.length&&n.hasClass(T.css.colgroup)&&n.remove(),o.widthFixed&&0===o.$table.children("colgroup").length){for(n=R('<colgroup class="'+T.css.colgroup+'">'),t=o.$table.width(),s=(a=o.$tbodies.find("tr:first").children(":visible")).length,i=0;i<s;i++)r=parseInt(a.eq(i).width()/t*1e3,10)/10+"%",n.append(R("<col>").css("width",r));o.$table.prepend(n)}},getData:function(e,t,r){var a,s,i="",o=R(e);return o.length?(a=!!R.metadata&&o.metadata(),s=" "+(o.attr("class")||""),void 0!==o.data(r)||void 0!==o.data(r.toLowerCase())?i+=o.data(r)||o.data(r.toLowerCase()):a&&void 0!==a[r]?i+=a[r]:t&&void 0!==t[r]?i+=t[r]:" "!==s&&s.match(" "+r+"-")&&(i=s.match(new RegExp("\\s"+r+"-([\\w-]+)"))[1]||""),R.trim(i)):""},getColumnData:function(e,t,r,a,s){if("object"!=typeof t||null===t)return t;var i,o=(e=R(e)[0]).config,n=s||o.$headers,l=o.$headerIndexed&&o.$headerIndexed[r]||n.find('[data-column="'+r+'"]:last');if(void 0!==t[r])return a?t[r]:t[n.index(l)];for(i in t)if("string"==typeof i&&l.filter(i).add(l.find(i)).length)return t[i]},isProcessing:function(e,t,r){var a=(e=R(e))[0].config,s=r||e.find("."+T.css.header);t?(void 0!==r&&0<a.sortList.length&&(s=s.filter(function(){return!this.sortDisabled&&0<=T.isValueInArray(parseFloat(R(this).attr("data-column")),a.sortList)})),e.add(s).addClass(T.css.processing+" "+a.cssProcessing)):e.add(s).removeClass(T.css.processing+" "+a.cssProcessing)},processTbody:function(e,t,r){if(e=R(e)[0],r)return e.isProcessing=!0,t.before('<colgroup class="tablesorter-savemyplace"/>'),R.fn.detach?t.detach():t.remove();var a=R(e).find("colgroup.tablesorter-savemyplace");t.insertAfter(a),a.remove(),e.isProcessing=!1},clearTableBody:function(e){R(e)[0].config.$tbodies.children().detach()},characterEquivalents:{a:"áàâãäąå",A:"ÁÀÂÃÄĄÅ",c:"çćč",C:"ÇĆČ",e:"éèêëěę",E:"ÉÈÊËĚĘ",i:"íìİîïı",I:"ÍÌİÎÏ",o:"óòôõöō",O:"ÓÒÔÕÖŌ",ss:"ß",SS:"ẞ",u:"úùûüů",U:"ÚÙÛÜŮ"},replaceAccents:function(e){var t,r="[",a=T.characterEquivalents;if(!T.characterRegex){for(t in T.characterRegexArray={},a)"string"==typeof t&&(r+=a[t],T.characterRegexArray[t]=new RegExp("["+a[t]+"]","g"));T.characterRegex=new RegExp(r+"]")}if(T.characterRegex.test(e))for(t in a)"string"==typeof t&&(e=e.replace(T.characterRegexArray[t],t));return e},validateOptions:function(e){var t,r,a,s,i="headers sortForce sortList sortAppend widgets".split(" "),o=e.originalSettings;if(o){for(t in T.debug(e,"core")&&(s=new Date),o)if("undefined"===(a=typeof T.defaults[t]))console.warn('Tablesorter Warning! "table.config.'+t+'" option not recognized');else if("object"===a)for(r in o[t])a=T.defaults[t]&&typeof T.defaults[t][r],R.inArray(t,i)<0&&"undefined"===a&&console.warn('Tablesorter Warning! "table.config.'+t+"."+r+'" option not recognized');T.debug(e,"core")&&console.log("validate options time:"+T.benchmark(s))}},restoreHeaders:function(e){var t,r,a=R(e)[0].config,s=a.$table.find(a.selectorHeaders),i=s.length;for(t=0;t<i;t++)(r=s.eq(t)).find("."+T.css.headerIn).length&&r.html(a.headerContent[t])},destroy:function(e,t,r){if((e=R(e)[0]).hasInitialized){T.removeWidget(e,!0,!1);var a,s=R(e),i=e.config,o=s.find("thead:first"),n=o.find("tr."+T.css.headerRow).removeClass(T.css.headerRow+" "+i.cssHeaderRow),l=s.find("tfoot:first > tr").children("th, td");!1===t&&0<=R.inArray("uitheme",i.widgets)&&(s.triggerHandler("applyWidgetId",["uitheme"]),s.triggerHandler("applyWidgetId",["zebra"])),o.find("tr").not(n).remove(),a="sortReset update updateRows updateAll updateHeaders updateCell addRows updateComplete sorton appendCache updateCache applyWidgetId applyWidgets refreshWidgets removeWidget destroy mouseup mouseleave "+"keypress sortBegin sortEnd resetToLoadState ".split(" ").join(i.namespace+" "),s.removeData("tablesorter").unbind(a.replace(T.regex.spaces," ")),i.$headers.add(l).removeClass([T.css.header,i.cssHeader,i.cssAsc,i.cssDesc,T.css.sortAsc,T.css.sortDesc,T.css.sortNone].join(" ")).removeAttr("data-column").removeAttr("aria-label").attr("aria-disabled","true"),n.find(i.selectorSort).unbind("mousedown mouseup keypress ".split(" ").join(i.namespace+" ").replace(T.regex.spaces," ")),T.restoreHeaders(e),s.toggleClass(T.css.table+" "+i.tableClass+" tablesorter-"+i.theme,!1===t),s.removeClass(i.namespace.slice(1)),e.hasInitialized=!1,delete e.config.cache,"function"==typeof r&&r(e),T.debug(i,"core")&&console.log("tablesorter has been removed")}}};R.fn.tablesorter=function(t){return this.each(function(){var e=R.extend(!0,{},T.defaults,t,T.instanceMethods);e.originalSettings=t,!this.hasInitialized&&T.buildTable&&"TABLE"!==this.nodeName?T.buildTable(this,e):T.setup(this,e)})},window.console&&window.console.log||(T.logs=[],console={},console.log=console.warn=console.error=console.table=function(){var e=1<arguments.length?arguments:arguments[0];T.logs[T.logs.length]={date:Date.now(),log:e}}),T.addParser({id:"no-parser",is:function(){return!1},format:function(){return""},type:"text"}),T.addParser({id:"text",is:function(){return!0},format:function(e,t){var r=t.config;return e&&(e=R.trim(r.ignoreCase?e.toLocaleLowerCase():e),e=r.sortLocaleCompare?T.replaceAccents(e):e),e},type:"text"}),T.regex.nondigit=/[^\w,. \-()]/g,T.addParser({id:"digit",is:function(e){return T.isDigit(e)},format:function(e,t){var r=T.formatFloat((e||"").replace(T.regex.nondigit,""),t);return e&&"number"==typeof r?r:e?R.trim(e&&t.config.ignoreCase?e.toLocaleLowerCase():e):e},type:"numeric"}),T.regex.currencyReplace=/[+\-,. ]/g,T.regex.currencyTest=/^\(?\d+[\u00a3$\u20ac\u00a4\u00a5\u00a2?.]|[\u00a3$\u20ac\u00a4\u00a5\u00a2?.]\d+\)?$/,T.addParser({id:"currency",is:function(e){return e=(e||"").replace(T.regex.currencyReplace,""),T.regex.currencyTest.test(e)},format:function(e,t){var r=T.formatFloat((e||"").replace(T.regex.nondigit,""),t);return e&&"number"==typeof r?r:e?R.trim(e&&t.config.ignoreCase?e.toLocaleLowerCase():e):e},type:"numeric"}),T.regex.urlProtocolTest=/^(https?|ftp|file):\/\//,T.regex.urlProtocolReplace=/(https?|ftp|file):\/\/(www\.)?/,T.addParser({id:"url",is:function(e){return T.regex.urlProtocolTest.test(e)},format:function(e){return e?R.trim(e.replace(T.regex.urlProtocolReplace,"")):e},type:"text"}),T.regex.dash=/-/g,T.regex.isoDate=/^\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2}/,T.addParser({id:"isoDate",is:function(e){return T.regex.isoDate.test(e)},format:function(e){var t=e?new Date(e.replace(T.regex.dash,"/")):e;return t instanceof Date&&isFinite(t)?t.getTime():e},type:"numeric"}),T.regex.percent=/%/g,T.regex.percentTest=/(\d\s*?%|%\s*?\d)/,T.addParser({id:"percent",is:function(e){return T.regex.percentTest.test(e)&&e.length<15},format:function(e,t){return e?T.formatFloat(e.replace(T.regex.percent,""),t):e},type:"numeric"}),T.addParser({id:"image",is:function(e,t,r,a){return 0<a.find("img").length},format:function(e,t,r){return R(r).find("img").attr(t.config.imgAttr||"alt")||e},parsed:!0,type:"text"}),T.regex.dateReplace=/(\S)([AP]M)$/i,T.regex.usLongDateTest1=/^[A-Z]{3,10}\.?\s+\d{1,2},?\s+(\d{4})(\s+\d{1,2}:\d{2}(:\d{2})?(\s+[AP]M)?)?$/i,T.regex.usLongDateTest2=/^\d{1,2}\s+[A-Z]{3,10}\s+\d{4}/i,T.addParser({id:"usLongDate",is:function(e){return T.regex.usLongDateTest1.test(e)||T.regex.usLongDateTest2.test(e)},format:function(e){var t=e?new Date(e.replace(T.regex.dateReplace,"$1 $2")):e;return t instanceof Date&&isFinite(t)?t.getTime():e},type:"numeric"}),T.regex.shortDateTest=/(^\d{1,2}[\/\s]\d{1,2}[\/\s]\d{4})|(^\d{4}[\/\s]\d{1,2}[\/\s]\d{1,2})/,T.regex.shortDateReplace=/[\-.,]/g,T.regex.shortDateXXY=/(\d{1,2})[\/\s](\d{1,2})[\/\s](\d{4})/,T.regex.shortDateYMD=/(\d{4})[\/\s](\d{1,2})[\/\s](\d{1,2})/,T.convertFormat=function(e,t){e=(e||"").replace(T.regex.spaces," ").replace(T.regex.shortDateReplace,"/"),"mmddyyyy"===t?e=e.replace(T.regex.shortDateXXY,"$3/$1/$2"):"ddmmyyyy"===t?e=e.replace(T.regex.shortDateXXY,"$3/$2/$1"):"yyyymmdd"===t&&(e=e.replace(T.regex.shortDateYMD,"$1/$2/$3"));var r=new Date(e);return r instanceof Date&&isFinite(r)?r.getTime():""},T.addParser({id:"shortDate",is:function(e){return e=(e||"").replace(T.regex.spaces," ").replace(T.regex.shortDateReplace,"/"),T.regex.shortDateTest.test(e)},format:function(e,t,r,a){if(e){var s=t.config,i=s.$headerIndexed[a],o=i.length&&i.data("dateFormat")||T.getData(i,T.getColumnData(t,s.headers,a),"dateFormat")||s.dateFormat;return i.length&&i.data("dateFormat",o),T.convertFormat(e,o)||e}return e},type:"numeric"}),T.regex.timeTest=/^(0?[1-9]|1[0-2]):([0-5]\d)(\s[AP]M)$|^((?:[01]\d|[2][0-4]):[0-5]\d)$/i,T.regex.timeMatch=/(0?[1-9]|1[0-2]):([0-5]\d)(\s[AP]M)|((?:[01]\d|[2][0-4]):[0-5]\d)/i,T.addParser({id:"time",is:function(e){return T.regex.timeTest.test(e)},format:function(e){var t=(e||"").match(T.regex.timeMatch),r=new Date(e),a=e&&(null!==t?t[0]:"00:00 AM"),s=a?new Date("2000/01/01 "+a.replace(T.regex.dateReplace,"$1 $2")):a;return s instanceof Date&&isFinite(s)?(r instanceof Date&&isFinite(r)?r.getTime():0)?parseFloat(s.getTime()+"."+r.getTime()):s.getTime():e},type:"numeric"}),T.addParser({id:"metadata",is:function(){return!1},format:function(e,t,r){var a=t.config,s=a.parserMetadataName?a.parserMetadataName:"sortValue";return R(r).metadata()[s]},type:"numeric"}),T.addWidget({id:"zebra",priority:90,format:function(e,t,r){var a,s,i,o,n,l,c,d=new RegExp(t.cssChildRow,"i"),f=t.$tbodies.add(R(t.namespace+"_extra_table").children("tbody:not(."+t.cssInfoBlock+")"));for(n=0;n<f.length;n++)for(i=0,c=(a=f.eq(n).children("tr:visible").not(t.selectorRemove)).length,l=0;l<c;l++)s=a.eq(l),d.test(s[0].className)||i++,o=i%2==0,s.removeClass(r.zebra[o?1:0]).addClass(r.zebra[o?0:1])},remove:function(e,t,r,a){if(!a){var s,i,o=t.$tbodies,n=(r.zebra||["even","odd"]).join(" ");for(s=0;s<o.length;s++)(i=T.processTbody(e,o.eq(s),!0)).children().removeClass(n),T.processTbody(e,i,!1)}}})}(e),function(b,y,v){"use strict";var w=b.tablesorter||{};b.extend(!0,w.defaults,{fixedUrl:"",widgetOptions:{storage_fixedUrl:"",storage_group:"",storage_page:"",storage_storageType:"",storage_tableId:"",storage_useSessionStorage:""}}),w.storage=function(e,t,r,a){var s,i,o,n=!1,l={},c=(e=b(e)[0]).config,d=c&&c.widgetOptions,f=w.debug(c,"storage"),u=(a&&a.storageType||d&&d.storage_storageType).toString().charAt(0).toLowerCase(),g=u?"":a&&a.useSessionStorage||d&&d.storage_useSessionStorage,p=b(e),h=a&&a.id||p.attr(a&&a.group||d&&d.storage_group||"data-table-group")||d&&d.storage_tableId||e.id||b(".tablesorter").index(p),m=a&&a.url||p.attr(a&&a.page||d&&d.storage_page||"data-table-page")||d&&d.storage_fixedUrl||c&&c.fixedUrl||y.location.pathname;if("c"!==u&&(u="s"===u||g?"sessionStorage":"localStorage")in y)try{y[u].setItem("_tmptest","temp"),n=!0,y[u].removeItem("_tmptest")}catch(e){console.warn(u+" is not supported in this browser")}if(f&&console.log("Storage >> Using",n?u:"cookies"),b.parseJSON&&(l=n?b.parseJSON(y[u][t]||"null")||{}:(i=v.cookie.split(/[;\s|=]/),0!==(s=b.inArray(t,i)+1)&&b.parseJSON(i[s]||"null")||{})),void 0===r||!y.JSON||!JSON.hasOwnProperty("stringify"))return l&&l[m]?l[m][h]:"";l[m]||(l[m]={}),l[m][h]=r,n?y[u][t]=JSON.stringify(l):((o=new Date).setTime(o.getTime()+31536e6),v.cookie=t+"="+JSON.stringify(l).replace(/\"/g,'"')+"; expires="+o.toGMTString()+"; path=/")}}(e,window,document),function($){"use strict";var S=$.tablesorter||{};S.themes={bootstrap:{table:"table table-bordered table-striped",caption:"caption",header:"bootstrap-header",sortNone:"",sortAsc:"",sortDesc:"",active:"",hover:"",icons:"",iconSortNone:"bootstrap-icon-unsorted",iconSortAsc:"glyphicon glyphicon-chevron-up",iconSortDesc:"glyphicon glyphicon-chevron-down",filterRow:"",footerRow:"",footerCells:"",even:"",odd:""},jui:{table:"ui-widget ui-widget-content ui-corner-all",caption:"ui-widget-content",header:"ui-widget-header ui-corner-all ui-state-default",sortNone:"",sortAsc:"",sortDesc:"",active:"ui-state-active",hover:"ui-state-hover",icons:"ui-icon",iconSortNone:"ui-icon-carat-2-n-s ui-icon-caret-2-n-s",iconSortAsc:"ui-icon-carat-1-n ui-icon-caret-1-n",iconSortDesc:"ui-icon-carat-1-s ui-icon-caret-1-s",filterRow:"",footerRow:"",footerCells:"",even:"ui-widget-content",odd:"ui-state-default"}},$.extend(S.css,{wrapper:"tablesorter-wrapper"}),S.addWidget({id:"uitheme",priority:10,format:function(e,t,r){var a,s,i,o,n,l,c,d,f,u,g,p,h,m=S.themes,b=t.$table.add($(t.namespace+"_extra_table")),y=t.$headers.add($(t.namespace+"_extra_headers")),v=t.theme||"jui",w=m[v]||{},x=$.trim([w.sortNone,w.sortDesc,w.sortAsc,w.active].join(" ")),C=$.trim([w.iconSortNone,w.iconSortDesc,w.iconSortAsc].join(" ")),_=S.debug(t,"uitheme");for(_&&(n=new Date),b.hasClass("tablesorter-"+v)&&t.theme===t.appliedTheme&&r.uitheme_applied||(r.uitheme_applied=!0,u=m[t.appliedTheme]||{},g=(h=!$.isEmptyObject(u))?[u.sortNone,u.sortDesc,u.sortAsc,u.active].join(" "):"",p=h?[u.iconSortNone,u.iconSortDesc,u.iconSortAsc].join(" "):"",h&&(r.zebra[0]=$.trim(" "+r.zebra[0].replace(" "+u.even,"")),r.zebra[1]=$.trim(" "+r.zebra[1].replace(" "+u.odd,"")),t.$tbodies.children().removeClass([u.even,u.odd].join(" "))),w.even&&(r.zebra[0]+=" "+w.even),w.odd&&(r.zebra[1]+=" "+w.odd),b.children("caption").removeClass(u.caption||"").addClass(w.caption),d=b.removeClass((t.appliedTheme?"tablesorter-"+(t.appliedTheme||""):"")+" "+(u.table||"")).addClass("tablesorter-"+v+" "+(w.table||"")).children("tfoot"),t.appliedTheme=t.theme,d.length&&d.children("tr").removeClass(u.footerRow||"").addClass(w.footerRow).children("th, td").removeClass(u.footerCells||"").addClass(w.footerCells),y.removeClass((h?[u.header,u.hover,g].join(" "):"")||"").addClass(w.header).not(".sorter-false").unbind("mouseenter.tsuitheme mouseleave.tsuitheme").bind("mouseenter.tsuitheme mouseleave.tsuitheme",function(e){$(this)["mouseenter"===e.type?"addClass":"removeClass"](w.hover||"")}),y.each(function(){var e=$(this);e.find("."+S.css.wrapper).length||e.wrapInner('<div class="'+S.css.wrapper+'" style="position:relative;height:100%;width:100%"></div>')}),t.cssIcon&&y.find("."+S.css.icon).removeClass(h?[u.icons,p].join(" "):"").addClass(w.icons||""),S.hasWidget(t.table,"filter")&&(s=function(){b.children("thead").children("."+S.css.filterRow).removeClass(h&&u.filterRow||"").addClass(w.filterRow||"")},r.filter_initialized?s():b.one("filterInit",function(){s()}))),a=0;a<t.columns;a++)l=t.$headers.add($(t.namespace+"_extra_headers")).not(".sorter-false").filter('[data-column="'+a+'"]'),c=S.css.icon?l.find("."+S.css.icon):$(),(f=y.not(".sorter-false").filter('[data-column="'+a+'"]:last')).length&&(l.removeClass(x),c.removeClass(C),f[0].sortDisabled?c.removeClass(w.icons||""):(i=w.sortNone,o=w.iconSortNone,f.hasClass(S.css.sortAsc)?(i=[w.sortAsc,w.active].join(" "),o=w.iconSortAsc):f.hasClass(S.css.sortDesc)&&(i=[w.sortDesc,w.active].join(" "),o=w.iconSortDesc),l.addClass(i),c.addClass(o||"")));_&&console.log("uitheme >> Applied "+v+" theme"+S.benchmark(n))},remove:function(e,t,r,a){if(r.uitheme_applied){var s=t.$table,i=t.appliedTheme||"jui",o=S.themes[i]||S.themes.jui,n=s.children("thead").children(),l=o.sortNone+" "+o.sortDesc+" "+o.sortAsc,c=o.iconSortNone+" "+o.iconSortDesc+" "+o.iconSortAsc;s.removeClass("tablesorter-"+i+" "+o.table),r.uitheme_applied=!1,a||(s.find(S.css.header).removeClass(o.header),n.unbind("mouseenter.tsuitheme mouseleave.tsuitheme").removeClass(o.hover+" "+l+" "+o.active).filter("."+S.css.filterRow).removeClass(o.filterRow),n.find("."+S.css.icon).removeClass(o.icons+" "+c))}}})}(e),function(b){"use strict";var y=b.tablesorter||{};y.addWidget({id:"columns",priority:65,options:{columns:["primary","secondary","tertiary"]},format:function(e,t,r){var a,s,i,o,n,l,c,d,f=t.$table,u=t.$tbodies,g=t.sortList,p=g.length,h=r&&r.columns||["primary","secondary","tertiary"],m=h.length-1;for(c=h.join(" "),s=0;s<u.length;s++)(i=(a=y.processTbody(e,u.eq(s),!0)).children("tr")).each(function(){if(n=b(this),"none"!==this.style.display&&(l=n.children().removeClass(c),g&&g[0]&&(l.eq(g[0][0]).addClass(h[0]),1<p)))for(d=1;d<p;d++)l.eq(g[d][0]).addClass(h[d]||h[m])}),y.processTbody(e,a,!1);if(o=!1!==r.columns_thead?["thead tr"]:[],!1!==r.columns_tfoot&&o.push("tfoot tr"),o.length&&(i=f.find(o.join(",")).children().removeClass(c),p))for(d=0;d<p;d++)i.filter('[data-column="'+g[d][0]+'"]').addClass(h[d]||h[m])},remove:function(e,t,r){var a,s,i=t.$tbodies,o=(r.columns||["primary","secondary","tertiary"]).join(" ");for(t.$headers.removeClass(o),t.$table.children("tfoot").children("tr").children("th, td").removeClass(o),a=0;a<i.length;a++)(s=y.processTbody(e,i.eq(a),!0)).children("tr").each(function(){b(this).children().removeClass(o)}),y.processTbody(e,s,!1)}})}(e),function(A){"use strict";var D,H,N=A.tablesorter||{},b=N.css,l=N.keyCodes;A.extend(b,{filterRow:"tablesorter-filter-row",filter:"tablesorter-filter",filterDisabled:"disabled",filterRowHide:"hideme"}),A.extend(l,{backSpace:8,escape:27,space:32,left:37,down:40}),N.addWidget({id:"filter",priority:50,options:{filter_cellFilter:"",filter_childRows:!1,filter_childByColumn:!1,filter_childWithSibs:!0,filter_columnAnyMatch:!0,filter_columnFilters:!0,filter_cssFilter:"",filter_defaultAttrib:"data-value",filter_defaultFilter:{},filter_excludeFilter:{},filter_external:"",filter_filteredRow:"filtered",filter_filterLabel:'Filter "{{label}}" column by...',filter_formatter:null,filter_functions:null,filter_hideEmpty:!0,filter_hideFilters:!1,filter_ignoreCase:!0,filter_liveSearch:!0,filter_matchType:{input:"exact",select:"exact"},filter_onlyAvail:"filter-onlyAvail",filter_placeholder:{search:"",select:""},filter_reset:null,filter_resetOnEsc:!0,filter_saveFilters:!1,filter_searchDelay:300,filter_searchFiltered:!0,filter_selectSource:null,filter_selectSourceSeparator:"|",filter_serversideFiltering:!1,filter_startsWith:!1,filter_useParsedData:!1},format:function(e,t,r){t.$table.hasClass("hasFilters")||D.init(e,t,r)},remove:function(e,t,r,a){var s,i,o=t.$table,n=t.$tbodies,l="addRows updateCell update updateRows updateComplete appendCache filterReset filterAndSortReset filterFomatterUpdate filterEnd search stickyHeadersInit ".split(" ").join(t.namespace+"filter ");if(o.removeClass("hasFilters").unbind(l.replace(N.regex.spaces," ")).find("."+b.filterRow).remove(),r.filter_initialized=!1,!a){for(s=0;s<n.length;s++)(i=N.processTbody(e,n.eq(s),!0)).children().removeClass(r.filter_filteredRow).show(),N.processTbody(e,i,!1);r.filter_reset&&A(document).undelegate(r.filter_reset,"click"+t.namespace+"filter")}}}),H=(D=N.filter={regex:{regex:/^\/((?:\\\/|[^\/])+)\/([migyu]{0,5})?$/,child:/tablesorter-childRow/,filtered:/filtered/,type:/undefined|number/,exact:/(^[\"\'=]+)|([\"\'=]+$)/g,operators:/[<>=]/g,query:"(q|query)",wild01:/\?/g,wild0More:/\*/g,quote:/\"/g,isNeg1:/(>=?\s*-\d)/,isNeg2:/(<=?\s*\d)/},types:{or:function(e,t,r){if(!H.orTest.test(t.iFilter)&&!H.orSplit.test(t.filter)||H.regex.test(t.filter))return null;var a,s,i,o=A.extend({},t),n=t.filter.split(H.orSplit),l=t.iFilter.split(H.orSplit),c=n.length;for(a=0;a<c;a++){o.nestedFilters=!0,o.filter=""+(D.parseFilter(e,n[a],t)||""),o.iFilter=""+(D.parseFilter(e,l[a],t)||""),i="("+(D.parseFilter(e,o.filter,t)||"")+")";try{if(s=new RegExp(t.isMatch?i:"^"+i+"$",e.widgetOptions.filter_ignoreCase?"i":"").test(o.exact)||D.processTypes(e,o,r))return s}catch(e){return null}}return s||!1},and:function(e,t,r){if(H.andTest.test(t.filter)){var a,s,i,o,n=A.extend({},t),l=t.filter.split(H.andSplit),c=t.iFilter.split(H.andSplit),d=l.length;for(a=0;a<d;a++){n.nestedFilters=!0,n.filter=""+(D.parseFilter(e,l[a],t)||""),n.iFilter=""+(D.parseFilter(e,c[a],t)||""),o=("("+(D.parseFilter(e,n.filter,t)||"")+")").replace(H.wild01,"\\S{1}").replace(H.wild0More,"\\S*");try{i=new RegExp(t.isMatch?o:"^"+o+"$",e.widgetOptions.filter_ignoreCase?"i":"").test(n.exact)||D.processTypes(e,n,r),s=0===a?i:s&&i}catch(e){return null}}return s||!1}return null},regex:function(e,t){if(H.regex.test(t.filter)){var r,a=t.filter_regexCache[t.index]||H.regex.exec(t.filter),s=a instanceof RegExp;try{s||(t.filter_regexCache[t.index]=a=new RegExp(a[1],a[2])),r=a.test(t.exact)}catch(e){r=!1}return r}return null},operators:function(e,t){if(H.operTest.test(t.iFilter)&&""!==t.iExact){var r,a,s,i=e.table,o=t.parsed[t.index],n=N.formatFloat(t.iFilter.replace(H.operators,""),i),l=e.parsers[t.index]||{},c=n;return!o&&"numeric"!==l.type||(s=A.trim(""+t.iFilter.replace(H.operators,"")),n="number"!=typeof(a=D.parseFilter(e,s,t,!0))||""===a||isNaN(a)?n:a),r=!o&&"numeric"!==l.type||isNaN(n)||void 0===t.cache?(s=isNaN(t.iExact)?t.iExact.replace(N.regex.nondigit,""):t.iExact,N.formatFloat(s,i)):t.cache,H.gtTest.test(t.iFilter)?a=H.gteTest.test(t.iFilter)?n<=r:n<r:H.ltTest.test(t.iFilter)&&(a=H.lteTest.test(t.iFilter)?r<=n:r<n),a||""!==c||(a=!0),a}return null},notMatch:function(e,t){if(H.notTest.test(t.iFilter)){var r,a=t.iFilter.replace("!",""),s=D.parseFilter(e,a,t)||"";return H.exact.test(s)?""===(s=s.replace(H.exact,""))||A.trim(s)!==t.iExact:(r=t.iExact.search(A.trim(s)),""===s||(t.anyMatch?r<0:!(e.widgetOptions.filter_startsWith?0===r:0<=r)))}return null},exact:function(e,t){if(H.exact.test(t.iFilter)){var r=t.iFilter.replace(H.exact,""),a=D.parseFilter(e,r,t)||"";return t.anyMatch?0<=A.inArray(a,t.rowArray):a==t.iExact}return null},range:function(e,t){if(H.toTest.test(t.iFilter)){var r,a,s,i,o=e.table,n=t.index,l=t.parsed[n],c=t.iFilter.split(H.toSplit);return a=c[0].replace(N.regex.nondigit,"")||"",s=N.formatFloat(D.parseFilter(e,a,t),o),a=c[1].replace(N.regex.nondigit,"")||"",i=N.formatFloat(D.parseFilter(e,a,t),o),!l&&"numeric"!==e.parsers[n].type||(s=""===(r=e.parsers[n].format(""+c[0],o,e.$headers.eq(n),n))||isNaN(r)?s:r,i=""===(r=e.parsers[n].format(""+c[1],o,e.$headers.eq(n),n))||isNaN(r)?i:r),r=!l&&"numeric"!==e.parsers[n].type||isNaN(s)||isNaN(i)?(a=isNaN(t.iExact)?t.iExact.replace(N.regex.nondigit,""):t.iExact,N.formatFloat(a,o)):t.cache,i<s&&(a=s,s=i,i=a),s<=r&&r<=i||""===s||""===i}return null},wild:function(e,t){if(H.wildOrTest.test(t.iFilter)){var r=""+(D.parseFilter(e,t.iFilter,t)||"");!H.wildTest.test(r)&&t.nestedFilters&&(r=t.isMatch?r:"^("+r+")$");try{return new RegExp(r.replace(H.wild01,"\\S{1}").replace(H.wild0More,"\\S*"),e.widgetOptions.filter_ignoreCase?"i":"").test(t.exact)}catch(e){return null}}return null},fuzzy:function(e,t){if(H.fuzzyTest.test(t.iFilter)){var r,a=0,s=t.iExact.length,i=t.iFilter.slice(1),o=D.parseFilter(e,i,t)||"";for(r=0;r<s;r++)t.iExact[r]===o[a]&&(a+=1);return a===o.length}return null}},init:function(r){N.language=A.extend(!0,{},{to:"to",or:"or",and:"and"},N.language);function e(e,t,r){return""===(t=t.trim())?"":(e||"")+t+(r||"")}var t,a,s,i,o,n,l,c,d=r.config,f=d.widgetOptions;if(d.$table.addClass("hasFilters"),d.lastSearch=[],f.filter_searchTimer=null,f.filter_initTimer=null,f.filter_formatterCount=0,f.filter_formatterInit=[],f.filter_anyColumnSelector='[data-column="all"],[data-column="any"]',f.filter_multipleColumnSelector='[data-column*="-"],[data-column*=","]',n="\\{"+H.query+"\\}",A.extend(H,{child:new RegExp(d.cssChildRow),filtered:new RegExp(f.filter_filteredRow),alreadyFiltered:new RegExp("(\\s+(-"+e("|",N.language.or)+e("|",N.language.to)+")\\s+)","i"),toTest:new RegExp("\\s+(-"+e("|",N.language.to)+")\\s+","i"),toSplit:new RegExp("(?:\\s+(?:-"+e("|",N.language.to)+")\\s+)","gi"),andTest:new RegExp("\\s+("+e("",N.language.and,"|")+"&&)\\s+","i"),andSplit:new RegExp("(?:\\s+(?:"+e("",N.language.and,"|")+"&&)\\s+)","gi"),orTest:new RegExp("(\\|"+e("|\\s+",N.language.or,"\\s+")+")","i"),orSplit:new RegExp("(?:\\|"+e("|\\s+(?:",N.language.or,")\\s+")+")","gi"),iQuery:new RegExp(n,"i"),igQuery:new RegExp(n,"ig"),operTest:/^[<>]=?/,gtTest:/>/,gteTest:/>=/,ltTest:/</,lteTest:/<=/,notTest:/^\!/,wildOrTest:/[\?\*\|]/,wildTest:/\?\*/,fuzzyTest:/^~/,exactTest:/[=\"\|!]/}),n=d.$headers.filter(".filter-false, .parser-false").length,!1!==f.filter_columnFilters&&n!==d.$headers.length&&D.buildRow(r,d,f),s="addRows updateCell update updateRows updateComplete appendCache filterReset "+"filterAndSortReset filterResetSaved filterEnd search ".split(" ").join(d.namespace+"filter "),d.$table.bind(s,function(e,t){return n=f.filter_hideEmpty&&A.isEmptyObject(d.cache)&&!(d.delayInit&&"appendCache"===e.type),d.$table.find("."+b.filterRow).toggleClass(f.filter_filteredRow,n),/(search|filter)/.test(e.type)||(e.stopPropagation(),D.buildDefault(r,!0)),"filterReset"===e.type||"filterAndSortReset"===e.type?(d.$table.find("."+b.filter).add(f.filter_$externalFilters).val(""),"filterAndSortReset"===e.type?N.sortReset(this.config,function(){D.searching(r,[])}):D.searching(r,[])):"filterResetSaved"===e.type?N.storage(r,"tablesorter-filters",""):"filterEnd"===e.type?D.buildDefault(r,!0):(t="search"===e.type?t:"updateComplete"===e.type?d.$table.data("lastSearch"):"",/(update|add)/.test(e.type)&&"updateComplete"!==e.type&&(d.lastCombinedFilter=null,d.lastSearch=[],setTimeout(function(){d.$table.triggerHandler("filterFomatterUpdate")},100)),D.searching(r,t,!0)),!1}),f.filter_reset&&(f.filter_reset instanceof A?f.filter_reset.click(function(){d.$table.triggerHandler("filterReset")}):A(f.filter_reset).length&&A(document).undelegate(f.filter_reset,"click"+d.namespace+"filter").delegate(f.filter_reset,"click"+d.namespace+"filter",function(){d.$table.triggerHandler("filterReset")})),f.filter_functions)for(o=0;o<d.columns;o++)if(l=N.getColumnData(r,f.filter_functions,o))if(c=!((i=d.$headerIndexed[o].removeClass("filter-select")).hasClass("filter-false")||i.hasClass("parser-false")),!(t="")===l&&c)D.buildSelect(r,o);else if("object"==typeof l&&c){for(a in l)"string"==typeof a&&(t+=""===t?'<option value="">'+(i.data("placeholder")||i.attr("data-placeholder")||f.filter_placeholder.select||"")+"</option>":"",0<=(s=n=a).indexOf(f.filter_selectSourceSeparator)&&(s=(n=a.split(f.filter_selectSourceSeparator))[1],n=n[0]),t+="<option "+(s===n?"":'data-function-name="'+a+'" ')+'value="'+n+'">'+s+"</option>");d.$table.find("thead").find("select."+b.filter+'[data-column="'+o+'"]').append(t),(l="function"==typeof(s=f.filter_selectSource)||N.getColumnData(r,s,o))&&D.buildSelect(d.table,o,"",!0,i.hasClass(f.filter_onlyAvail))}D.buildDefault(r,!0),D.bindSearch(r,d.$table.find("."+b.filter),!0),f.filter_external&&D.bindSearch(r,f.filter_external),f.filter_hideFilters&&D.hideFilters(d),d.showProcessing&&(s="filterStart filterEnd ".split(" ").join(d.namespace+"filter-sp "),d.$table.unbind(s.replace(N.regex.spaces," ")).bind(s,function(e,t){i=t?d.$table.find("."+b.header).filter("[data-column]").filter(function(){return""!==t[A(this).data("column")]}):"",N.isProcessing(r,"filterStart"===e.type,t?i:"")})),d.filteredRows=d.totalRows,s="tablesorter-initialized pagerBeforeInitialized ".split(" ").join(d.namespace+"filter "),d.$table.unbind(s.replace(N.regex.spaces," ")).bind(s,function(){D.completeInit(this)}),d.pager&&d.pager.initialized&&!f.filter_initialized?(d.$table.triggerHandler("filterFomatterUpdate"),setTimeout(function(){D.filterInitComplete(d)},100)):f.filter_initialized||D.completeInit(r)},completeInit:function(e){var t=e.config,r=t.widgetOptions,a=D.setDefaults(e,t,r)||[];a.length&&(t.delayInit&&""===a.join("")||N.setFilters(e,a,!0)),t.$table.triggerHandler("filterFomatterUpdate"),setTimeout(function(){r.filter_initialized||D.filterInitComplete(t)},100)},formatterUpdated:function(e,t){var r=e&&e.closest("table"),a=r.length&&r[0].config,s=a&&a.widgetOptions;s&&!s.filter_initialized&&(s.filter_formatterInit[t]=1)},filterInitComplete:function(e){function t(){s.filter_initialized=!0,e.lastSearch=e.$table.data("lastSearch"),e.$table.triggerHandler("filterInit",e),D.findRows(e.table,e.lastSearch||[]),N.debug(e,"filter")&&console.log("Filter >> Widget initialized")}var r,a,s=e.widgetOptions,i=0;if(A.isEmptyObject(s.filter_formatter))t();else{for(a=s.filter_formatterInit.length,r=0;r<a;r++)1===s.filter_formatterInit[r]&&i++;clearTimeout(s.filter_initTimer),s.filter_initialized||i!==s.filter_formatterCount?s.filter_initialized||(s.filter_initTimer=setTimeout(function(){t()},500)):t()}},processFilters:function(e,t){var r,a=[],s=t?encodeURIComponent:decodeURIComponent,i=e.length;for(r=0;r<i;r++)e[r]&&(a[r]=s(e[r]));return a},setDefaults:function(e,t,r){var a,s,i,o,n,l=N.getFilters(e)||[];if(r.filter_saveFilters&&N.storage&&(s=N.storage(e,"tablesorter-filters")||[],(a=A.isArray(s))&&""===s.join("")||!a||(l=D.processFilters(s))),""===l.join(""))for(n=t.$headers.add(r.filter_$externalFilters).filter("["+r.filter_defaultAttrib+"]"),i=0;i<=t.columns;i++)o=i===t.columns?"all":i,l[i]=n.filter('[data-column="'+o+'"]').attr(r.filter_defaultAttrib)||l[i]||"";return t.$table.data("lastSearch",l),l},parseFilter:function(e,t,r,a){return a||r.parsed[r.index]?e.parsers[r.index].format(t,e.table,[],r.index):t},buildRow:function(e,t,r){var a,s,i,o,n,l,c,d,f,u=r.filter_cellFilter,g=t.columns,p=A.isArray(u),h='<tr role="search" class="'+b.filterRow+" "+t.cssIgnoreRow+'">';for(i=0;i<g;i++)t.$headerIndexed[i].length&&(h+=1<(f=t.$headerIndexed[i]&&t.$headerIndexed[i][0].colSpan||0)?'<td data-column="'+i+"-"+(i+f-1)+'" colspan="'+f+'"':'<td data-column="'+i+'"',h+=p?u[i]?' class="'+u[i]+'"':"":""!==u?' class="'+u+'"':"",h+="></td>");for(t.$filters=A(h+="</tr>").appendTo(t.$table.children("thead").eq(0)).children("td"),i=0;i<g;i++)l=!1,(o=t.$headerIndexed[i])&&o.length&&(a=D.getColumnElm(t,t.$filters,i),d=N.getColumnData(e,r.filter_functions,i),n=r.filter_functions&&d&&"function"!=typeof d||o.hasClass("filter-select"),s=N.getColumnData(e,t.headers,i),l="false"===N.getData(o[0],s,"filter")||"false"===N.getData(o[0],s,"parser"),n?h=A("<select>").appendTo(a):((d=N.getColumnData(e,r.filter_formatter,i))?(r.filter_formatterCount++,(h=d(a,i))&&0===h.length&&(h=a.children("input")),h&&(0===h.parent().length||h.parent().length&&h.parent()[0]!==a[0])&&a.append(h)):h=A('<input type="search">').appendTo(a),h&&(f=o.data("placeholder")||o.attr("data-placeholder")||r.filter_placeholder.search||"",h.attr("placeholder",f))),h&&(c=(A.isArray(r.filter_cssFilter)?void 0!==r.filter_cssFilter[i]&&r.filter_cssFilter[i]||"":r.filter_cssFilter)||"",h.addClass(b.filter+" "+c),f=(f=(c=r.filter_filterLabel).match(/{{([^}]+?)}}/g))||["{{label}}"],A.each(f,function(e,t){var r=new RegExp(t,"g"),a=o.attr("data-"+t.replace(/{{|}}/g,"")),s=void 0===a?o.text():a;c=c.replace(r,A.trim(s))}),h.attr({"data-column":a.attr("data-column"),"aria-label":c}),l&&(h.attr("placeholder","").addClass(b.filterDisabled)[0].disabled=!0)))},bindSearch:function(s,e,t){if(s=A(s)[0],(e=A(e)).length){var r,i=s.config,o=i.widgetOptions,a=i.namespace+"filter",n=o.filter_$externalFilters;!0!==t&&(r=o.filter_anyColumnSelector+","+o.filter_multipleColumnSelector,o.filter_$anyMatch=e.filter(r),n&&n.length?o.filter_$externalFilters=o.filter_$externalFilters.add(e):o.filter_$externalFilters=e,N.setFilters(s,i.$table.data("lastSearch")||[],!1===t)),r="keypress keyup keydown search change input ".split(" ").join(a+" "),e.attr("data-lastSearchTime",(new Date).getTime()).unbind(r.replace(N.regex.spaces," ")).bind("keydown"+a,function(e){if(e.which===l.escape&&!s.config.widgetOptions.filter_resetOnEsc)return!1}).bind("keyup"+a,function(e){o=s.config.widgetOptions;var t=parseInt(A(this).attr("data-column"),10),r="boolean"==typeof o.filter_liveSearch?o.filter_liveSearch:N.getColumnData(s,o.filter_liveSearch,t);if(void 0===r&&(r=o.filter_liveSearch.fallback||!1),A(this).attr("data-lastSearchTime",(new Date).getTime()),e.which===l.escape)this.value=o.filter_resetOnEsc?"":i.lastSearch[t];else{if(""!==this.value&&("number"==typeof r&&this.value.length<r||e.which!==l.enter&&e.which!==l.backSpace&&(e.which<l.space||e.which>=l.left&&e.which<=l.down)))return;if(!1===r&&""!==this.value&&e.which!==l.enter)return}D.searching(s,!0,!0,t)}).bind("search change keypress input blur ".split(" ").join(a+" "),function(e){var t=parseInt(A(this).attr("data-column"),10),r=e.type,a="boolean"==typeof o.filter_liveSearch?o.filter_liveSearch:N.getColumnData(s,o.filter_liveSearch,t);!s.config.widgetOptions.filter_initialized||e.which!==l.enter&&"search"!==r&&"blur"!==r&&("change"!==r&&"input"!==r||!0!==a&&(!0===a||"INPUT"===e.target.nodeName)||this.value===i.lastSearch[t])||(e.preventDefault(),A(this).attr("data-lastSearchTime",(new Date).getTime()),D.searching(s,"keypress"!==r||e.which===l.enter,!0,t))})}},searching:function(e,t,r,a){var s,i=e.config.widgetOptions;void 0===a?s=!1:void 0===(s="boolean"==typeof i.filter_liveSearch?i.filter_liveSearch:N.getColumnData(e,i.filter_liveSearch,a))&&(s=i.filter_liveSearch.fallback||!1),clearTimeout(i.filter_searchTimer),void 0===t||!0===t?i.filter_searchTimer=setTimeout(function(){D.checkFilters(e,t,r)},s?i.filter_searchDelay:10):D.checkFilters(e,t,r)},equalFilters:function(e,t,r){var a,s=[],i=[],o=e.columns+1;for(t=A.isArray(t)?t:[],r=A.isArray(r)?r:[],a=0;a<o;a++)s[a]=t[a]||"",i[a]=r[a]||"";return s.join(",")===i.join(",")},checkFilters:function(e,t,r){var a=e.config,s=a.widgetOptions,i=A.isArray(t),o=i?t:N.getFilters(e,!0),n=o||[];if(A.isEmptyObject(a.cache))a.delayInit&&(!a.pager||a.pager&&a.pager.initialized)&&N.updateCache(a,function(){D.checkFilters(e,!1,r)});else{if(i&&(N.setFilters(e,o,!1,!0!==r),s.filter_initialized||(a.lastSearch=[],a.lastCombinedFilter="")),s.filter_hideFilters&&a.$table.find("."+b.filterRow).triggerHandler(D.hideFiltersCheck(a)?"mouseleave":"mouseenter"),D.equalFilters(a,a.lastSearch,n)){if(!1!==t)return;a.lastCombinedFilter="",a.lastSearch=[]}if(o=o||[],o=Array.prototype.map?o.map(String):o.join("�").split("�"),s.filter_initialized&&a.$table.triggerHandler("filterStart",[o]),!a.showProcessing)return D.findRows(e,o,n),!1;setTimeout(function(){return D.findRows(e,o,n),!1},30)}},hideFiltersCheck:function(e){if("function"==typeof e.widgetOptions.filter_hideFilters){var t=e.widgetOptions.filter_hideFilters(e);if("boolean"==typeof t)return t}return""===N.getFilters(e.$table).join("")},hideFilters:function(a,e){var s;(e||a.$table).find("."+b.filterRow).addClass(b.filterRowHide).bind("mouseenter mouseleave",function(e){var t=e,r=A(this);clearTimeout(s),s=setTimeout(function(){/enter|over/.test(t.type)?r.removeClass(b.filterRowHide):A(document.activeElement).closest("tr")[0]!==r[0]&&r.toggleClass(b.filterRowHide,D.hideFiltersCheck(a))},200)}).find("input, select").bind("focus blur",function(e){var t=e,r=A(this).closest("tr");clearTimeout(s),s=setTimeout(function(){clearTimeout(s),r.toggleClass(b.filterRowHide,D.hideFiltersCheck(a)&&"focus"!==t.type)},200)})},defaultFilter:function(e,t){if(""===e)return e;var r=H.iQuery,a=t.match(H.igQuery).length,s=1<a?A.trim(e).split(/\s/):[A.trim(e)],i=s.length-1,o=0,n=t;for(i<1&&1<a&&(s[1]=s[0]);r.test(n);)n=n.replace(r,s[o++]||""),r.test(n)&&o<i&&""!==(s[o]||"")&&(n=t.replace(r,n));return n},getLatestSearch:function(e){return e?e.sort(function(e,t){return A(t).attr("data-lastSearchTime")-A(e).attr("data-lastSearchTime")}):e||A()},findRange:function(e,t,r){var a,s,i,o,n,l,c,d,f,u=[];if(/^[0-9]+$/.test(t))return[parseInt(t,10)];if(!r&&/-/.test(t))for(f=(s=t.match(/(\d+)\s*-\s*(\d+)/g))?s.length:0,d=0;d<f;d++){for(i=s[d].split(/\s*-\s*/),o=parseInt(i[0],10)||0,(n=parseInt(i[1],10)||e.columns-1)<o&&(a=o,o=n,n=a),n>=e.columns&&(n=e.columns-1);o<=n;o++)u[u.length]=o;t=t.replace(s[d],"")}if(!r&&/,/.test(t))for(f=(l=t.split(/\s*,\s*/)).length,c=0;c<f;c++)""!==l[c]&&(d=parseInt(l[c],10))<e.columns&&(u[u.length]=d);if(!u.length)for(d=0;d<e.columns;d++)u[u.length]=d;return u},getColumnElm:function(t,e,r){return e.filter(function(){var e=D.findRange(t,A(this).attr("data-column"));return-1<A.inArray(r,e)})},multipleColumns:function(e,t){var r=e.widgetOptions,a=r.filter_initialized||!t.filter(r.filter_anyColumnSelector).length,s=A.trim(D.getLatestSearch(t).attr("data-column")||"");return D.findRange(e,s,!a)},processTypes:function(e,t,r){var a,s=null,i=null;for(a in D.types)A.inArray(a,r.excludeMatch)<0&&null===i&&null!==(i=D.types[a](e,t,r))&&(t.matchedOn=a,s=i);return s},matchType:function(e,t){var r=e.widgetOptions,a=e.$headerIndexed[t];return!a.hasClass("filter-exact")&&(!!a.hasClass("filter-match")||(r.filter_columnFilters?a=e.$filters.find("."+b.filter).add(r.filter_$externalFilters).filter('[data-column="'+t+'"]'):r.filter_$externalFilters&&(a=r.filter_$externalFilters.filter('[data-column="'+t+'"]')),!!a.length&&"match"===e.widgetOptions.filter_matchType[(a[0].nodeName||"").toLowerCase()]))},processRow:function(t,r,e){var a,s,i,o,n,l=t.widgetOptions,c=!0,d=l.filter_$anyMatch&&l.filter_$anyMatch.length,f=l.filter_$anyMatch&&l.filter_$anyMatch.length?D.multipleColumns(t,l.filter_$anyMatch):[];if(r.$cells=r.$row.children(),r.matchedOn=null,r.anyMatchFlag&&1<f.length||r.anyMatchFilter&&!d){if(r.anyMatch=!0,r.isMatch=!0,r.rowArray=r.$cells.map(function(e){if(-1<A.inArray(e,f)||r.anyMatchFilter&&!d)return r.parsed[e]?n=r.cacheArray[e]:(n=r.rawArray[e],n=A.trim(l.filter_ignoreCase?n.toLowerCase():n),t.sortLocaleCompare&&(n=N.replaceAccents(n))),n}).get(),r.filter=r.anyMatchFilter,r.iFilter=r.iAnyMatchFilter,r.exact=r.rowArray.join(" "),r.iExact=l.filter_ignoreCase?r.exact.toLowerCase():r.exact,r.cache=r.cacheArray.slice(0,-1).join(" "),e.excludeMatch=e.noAnyMatch,null!==(s=D.processTypes(t,r,e)))c=s;else if(l.filter_startsWith)for(c=!1,f=Math.min(t.columns,r.rowArray.length);!c&&0<f;)f--,c=c||0===r.rowArray[f].indexOf(r.iFilter);else c=0<=(r.iExact+r.childRowText).indexOf(r.iFilter);if(r.anyMatch=!1,r.filters.join("")===r.filter)return c}for(f=0;f<t.columns;f++)r.filter=r.filters[f],r.index=f,e.excludeMatch=e.excludeFilter[f],r.filter&&(r.cache=r.cacheArray[f],a=r.parsed[f]?r.cache:r.rawArray[f]||"",r.exact=t.sortLocaleCompare?N.replaceAccents(a):a,r.iExact=!H.type.test(typeof r.exact)&&l.filter_ignoreCase?r.exact.toLowerCase():r.exact,r.isMatch=D.matchType(t,f),a=c,o=l.filter_columnFilters&&t.$filters.add(l.filter_$externalFilters).filter('[data-column="'+f+'"]').find("select option:selected").attr("data-function-name")||"",t.sortLocaleCompare&&(r.filter=N.replaceAccents(r.filter)),l.filter_defaultFilter&&H.iQuery.test(e.defaultColFilter[f])&&(r.filter=D.defaultFilter(r.filter,e.defaultColFilter[f])),r.iFilter=l.filter_ignoreCase?(r.filter||"").toLowerCase():r.filter,s=null,(i=e.functions[f])&&("function"==typeof i?s=i(r.exact,r.cache,r.filter,f,r.$row,t,r):"function"==typeof i[o||r.filter]&&(s=i[n=o||r.filter](r.exact,r.cache,r.filter,f,r.$row,t,r))),c=!!(a=null===s?(s=D.processTypes(t,r,e),n=!0===i&&("and"===r.matchedOn||"or"===r.matchedOn),null===s||n?!0===i?r.isMatch?0<=(""+r.iExact).search(r.iFilter):r.filter===r.exact:(n=(r.iExact+r.childRowText).indexOf(D.parseFilter(t,r.iFilter,r)),!l.filter_startsWith&&0<=n||l.filter_startsWith&&0===n):s):s)&&c);return c},findRows:function(e,r,t){if(!D.equalFilters(e.config,e.config.lastSearch,t)&&e.config.widgetOptions.filter_initialized){var a,s,i,o,n,l,c,d,f,u,g,p,h,m,b,y,v,w,x,C,_,$,S,z=A.extend([],r),F=e.config,R=F.widgetOptions,T=N.debug(F,"filter"),I={anyMatch:!1,filters:r,filter_regexCache:[]},k={noAnyMatch:["range","operators"],functions:[],excludeFilter:[],defaultColFilter:[],defaultAnyFilter:N.getColumnData(e,R.filter_defaultFilter,F.columns,!0)||""};for(I.parsed=[],f=0;f<F.columns;f++)I.parsed[f]=R.filter_useParsedData||F.parsers&&F.parsers[f]&&F.parsers[f].parsed||N.getData&&"parsed"===N.getData(F.$headerIndexed[f],N.getColumnData(e,F.headers,f),"filter")||F.$headerIndexed[f].hasClass("filter-parsed"),k.functions[f]=N.getColumnData(e,R.filter_functions,f)||F.$headerIndexed[f].hasClass("filter-select"),k.defaultColFilter[f]=N.getColumnData(e,R.filter_defaultFilter,f)||"",k.excludeFilter[f]=(N.getColumnData(e,R.filter_excludeFilter,f,!0)||"").split(/\s+/);for(T&&(console.log("Filter >> Starting filter widget search",r),m=new Date),F.filteredRows=0,t=z||[],c=F.totalRows=0;c<F.$tbodies.length;c++){if(d=N.processTbody(e,F.$tbodies.eq(c),!0),f=F.columns,s=F.cache[c].normalized,o=A(A.map(s,function(e){return e[f].$row.get()})),""===t.join("")||R.filter_serversideFiltering)o.removeClass(R.filter_filteredRow).not("."+F.cssChildRow).css("display","");else{if(a=(o=o.not("."+F.cssChildRow)).length,(R.filter_$anyMatch&&R.filter_$anyMatch.length||void 0!==r[F.columns])&&(I.anyMatchFlag=!0,I.anyMatchFilter=""+(r[F.columns]||R.filter_$anyMatch&&D.getLatestSearch(R.filter_$anyMatch).val()||""),R.filter_columnAnyMatch)){for(x=I.anyMatchFilter.split(H.andSplit),C=!1,y=0;y<x.length;y++)1<(_=x[y].split(":")).length&&(isNaN(_[0])?A.each(F.headerContent,function(e,t){-1<t.toLowerCase().indexOf(_[0])&&(r[$=e]=_[1])}):$=parseInt(_[0],10)-1,0<=$&&$<F.columns&&(r[$]=_[1],x.splice(y,1),y--,C=!0));C&&(I.anyMatchFilter=x.join(" && "))}if(w=R.filter_searchFiltered,g=F.lastSearch||F.$table.data("lastSearch")||[],w)for(y=0;y<f+1;y++)b=r[y]||"",w||(y=f),w=w&&g.length&&0===b.indexOf(g[y]||"")&&!H.alreadyFiltered.test(b)&&!H.exactTest.test(b)&&!(H.isNeg1.test(b)||H.isNeg2.test(b))&&!(""!==b&&F.$filters&&F.$filters.filter('[data-column="'+y+'"]').find("select").length&&!D.matchType(F,y));for(v=o.not("."+R.filter_filteredRow).length,w&&0===v&&(w=!1),T&&console.log("Filter >> Searching through "+(w&&v<a?v:"all")+" rows"),I.anyMatchFlag&&(F.sortLocaleCompare&&(I.anyMatchFilter=N.replaceAccents(I.anyMatchFilter)),R.filter_defaultFilter&&H.iQuery.test(k.defaultAnyFilter)&&(I.anyMatchFilter=D.defaultFilter(I.anyMatchFilter,k.defaultAnyFilter),w=!1),I.iAnyMatchFilter=R.filter_ignoreCase&&F.ignoreCase?I.anyMatchFilter.toLowerCase():I.anyMatchFilter),l=0;l<a;l++)if(S=o[l].className,!(l&&H.child.test(S)||w&&H.filtered.test(S))){if(I.$row=o.eq(l),I.rowIndex=l,I.cacheArray=s[l],i=I.cacheArray[F.columns],I.rawArray=i.raw,I.childRowText="",!R.filter_childByColumn){for(S="",u=i.child,y=0;y<u.length;y++)S+=" "+u[y].join(" ")||"";I.childRowText=R.filter_childRows?R.filter_ignoreCase?S.toLowerCase():S:""}if(p=!1,h=D.processRow(F,I,k),n=i.$row,b=!!h,u=i.$row.filter(":gt(0)"),R.filter_childRows&&u.length){if(R.filter_childByColumn)for(R.filter_childWithSibs||(u.addClass(R.filter_filteredRow),n=n.eq(0)),y=0;y<u.length;y++)I.$row=u.eq(y),I.cacheArray=i.child[y],I.rawArray=I.cacheArray,b=D.processRow(F,I,k),p=p||b,!R.filter_childWithSibs&&b&&u.eq(y).removeClass(R.filter_filteredRow);p=p||h}else p=b;n.toggleClass(R.filter_filteredRow,!p)[0].display=p?"":"none"}}F.filteredRows+=o.not("."+R.filter_filteredRow).length,F.totalRows+=o.length,N.processTbody(e,d,!1)}F.lastCombinedFilter=z.join(""),F.lastSearch=z,F.$table.data("lastSearch",z),R.filter_saveFilters&&N.storage&&N.storage(e,"tablesorter-filters",D.processFilters(z,!0)),T&&console.log("Filter >> Completed search"+N.benchmark(m)),R.filter_initialized&&(F.$table.triggerHandler("filterBeforeEnd",F),F.$table.triggerHandler("filterEnd",F)),setTimeout(function(){N.applyWidget(F.table)},0)}},getOptionSource:function(e,t,r){var a=(e=A(e)[0]).config,s=!1,i=a.widgetOptions.filter_selectSource,o=a.$table.data("lastSearch")||[],n="function"==typeof i||N.getColumnData(e,i,t);if(r&&""!==o[t]&&(r=!1),!0===n)s=i(e,t,r);else{if(n instanceof A||"string"===A.type(n)&&0<=n.indexOf("</option>"))return n;if(A.isArray(n))s=n;else if("object"===A.type(i)&&n&&null===(s=n(e,t,r)))return null}return!1===s&&(s=D.getOptions(e,t,r)),D.processOptions(e,t,s)},processOptions:function(s,i,r){if(!A.isArray(r))return!1;var o,e,t,a,n,l,c=(s=A(s)[0]).config,d=null!=i&&0<=i&&i<c.columns,f=d&&c.$headerIndexed[i].hasClass("filter-select-sort-desc"),u=[];if(r=A.grep(r,function(e,t){return!!e.text||A.inArray(e,r)===t}),d&&c.$headerIndexed[i].hasClass("filter-select-nosort"))return r;for(a=r.length,t=0;t<a;t++)l=(e=r[t]).text?e.text:e,n=(d&&c.parsers&&c.parsers.length&&c.parsers[i].format(l,s,[],i)||l).toString(),n=c.widgetOptions.filter_ignoreCase?n.toLowerCase():n,e.text?(e.parsed=n,u[u.length]=e):u[u.length]={text:e,parsed:n};for(o=c.textSorter||"",u.sort(function(e,t){var r=f?t.parsed:e.parsed,a=f?e.parsed:t.parsed;return d&&"function"==typeof o?o(r,a,!0,i,s):d&&"object"==typeof o&&o.hasOwnProperty(i)?o[i](r,a,!0,i,s):!N.sortNatural||N.sortNatural(r,a)}),r=[],a=u.length,t=0;t<a;t++)r[r.length]=u[t];return r},getOptions:function(e,t,r){var a,s,i,o,n,l,c,d,f=(e=A(e)[0]).config,u=f.widgetOptions,g=[];for(s=0;s<f.$tbodies.length;s++)for(n=f.cache[s],i=f.cache[s].normalized.length,a=0;a<i;a++)if(o=n.row?n.row[a]:n.normalized[a][f.columns].$row[0],!r||!o.className.match(u.filter_filteredRow))if(u.filter_useParsedData||f.parsers[t].parsed||f.$headerIndexed[t].hasClass("filter-parsed")){if(g[g.length]=""+n.normalized[a][t],u.filter_childRows&&u.filter_childByColumn)for(d=n.normalized[a][f.columns].$row.length-1,l=0;l<d;l++)g[g.length]=""+n.normalized[a][f.columns].child[l][t]}else if(g[g.length]=n.normalized[a][f.columns].raw[t],u.filter_childRows&&u.filter_childByColumn)for(d=n.normalized[a][f.columns].$row.length,l=1;l<d;l++)c=n.normalized[a][f.columns].$row.eq(l).children().eq(t),g[g.length]=""+N.getElementText(f,c,t);return g},buildSelect:function(e,t,r,a,s){if(e=A(e)[0],t=parseInt(t,10),e.config.cache&&!A.isEmptyObject(e.config.cache)){var i,o,n,l,c,d,f,u=e.config,g=u.widgetOptions,p=u.$headerIndexed[t],h='<option value="">'+(p.data("placeholder")||p.attr("data-placeholder")||g.filter_placeholder.select||"")+"</option>",m=u.$table.find("thead").find("select."+b.filter+'[data-column="'+t+'"]').val();if(void 0!==r&&""!==r||null!==(r=D.getOptionSource(e,t,s))){if(A.isArray(r)){for(i=0;i<r.length;i++)if((f=r[i]).text){for(o in f["data-function-name"]=void 0===f.value?f.text:f.value,h+="<option",f)f.hasOwnProperty(o)&&"text"!==o&&(h+=" "+o+'="'+f[o].replace(H.quote,"&quot;")+'"');f.value||(h+=' value="'+f.text.replace(H.quote,"&quot;")+'"'),h+=">"+f.text.replace(H.quote,"&quot;")+"</option>"}else""+f!="[object Object]"&&(0<=(o=n=f=(""+f).replace(H.quote,"&quot;")).indexOf(g.filter_selectSourceSeparator)&&(o=(l=n.split(g.filter_selectSourceSeparator))[0],n=l[1]),h+=""!==f?"<option "+(o===n?"":'data-function-name="'+f+'" ')+'value="'+o+'">'+n+"</option>":"");r=[]}c=(u.$filters?u.$filters:u.$table.children("thead")).find("."+b.filter),g.filter_$externalFilters&&(c=c&&c.length?c.add(g.filter_$externalFilters):g.filter_$externalFilters),(d=c.filter('select[data-column="'+t+'"]')).length&&(d[a?"html":"append"](h),A.isArray(r)||d.append(r).val(m),d.val(m))}}},buildDefault:function(e,t){var r,a,s,i=e.config,o=i.widgetOptions,n=i.columns;for(r=0;r<n;r++)s=!((a=i.$headerIndexed[r]).hasClass("filter-false")||a.hasClass("parser-false")),(a.hasClass("filter-select")||!0===N.getColumnData(e,o.filter_functions,r))&&s&&D.buildSelect(e,r,"",t,a.hasClass(o.filter_onlyAvail))}}).regex,N.getFilters=function(e,t,r,a){var s,i,o,n,l=[],c=e?A(e)[0].config:"",d=c?c.widgetOptions:"";if(!0!==t&&d&&!d.filter_columnFilters||A.isArray(r)&&D.equalFilters(c,r,c.lastSearch))return A(e).data("lastSearch")||[];if(c&&(c.$filters&&(i=c.$filters.find("."+b.filter)),d.filter_$externalFilters&&(i=i&&i.length?i.add(d.filter_$externalFilters):d.filter_$externalFilters),i&&i.length))for(l=r||[],s=0;s<c.columns+1;s++)n=s===c.columns?d.filter_anyColumnSelector+","+d.filter_multipleColumnSelector:'[data-column="'+s+'"]',(o=i.filter(n)).length&&(o=D.getLatestSearch(o),A.isArray(r)?(a&&1<o.length&&(o=o.slice(1)),s===c.columns&&(o=(n=o.filter(d.filter_anyColumnSelector)).length?n:o),o.val(r[s]).trigger("change"+c.namespace)):(l[s]=o.val()||"",s===c.columns?o.slice(1).filter('[data-column*="'+o.attr("data-column")+'"]').val(l[s]):o.slice(1).val(l[s])),s===c.columns&&o.length&&(d.filter_$anyMatch=o));return l},N.setFilters=function(e,t,r,a){var s=e?A(e)[0].config:"",i=N.getFilters(e,!0,t,a);return void 0===r&&(r=!0),s&&r&&(s.lastCombinedFilter=null,s.lastSearch=[],D.searching(s.table,t,a),s.$table.triggerHandler("filterFomatterUpdate")),0!==i.length}}(e),function(S,z){"use strict";var F=S.tablesorter||{};function R(e,t){var r=isNaN(t.stickyHeaders_offset)?S(t.stickyHeaders_offset):[];return r.length?r.height()||0:parseInt(t.stickyHeaders_offset,10)||0}S.extend(F.css,{sticky:"tablesorter-stickyHeader",stickyVis:"tablesorter-sticky-visible",stickyHide:"tablesorter-sticky-hidden",stickyWrap:"tablesorter-sticky-wrapper"}),F.addHeaderResizeEvent=function(e,t,r){if((e=S(e)[0]).config){var a=S.extend({},{timer:250},r),l=e.config,c=l.widgetOptions,s=function(e){var t,r,a,s,i,o,n=l.$headers.length;for(c.resize_flag=!0,r=[],t=0;t<n;t++)s=(a=l.$headers.eq(t)).data("savedSizes")||[0,0],i=a[0].offsetWidth,o=a[0].offsetHeight,i===s[0]&&o===s[1]||(a.data("savedSizes",[i,o]),r.push(a[0]));r.length&&!1!==e&&l.$table.triggerHandler("resize",[r]),c.resize_flag=!1};if(clearInterval(c.resize_timer),t)return c.resize_flag=!1;s(!1),c.resize_timer=setInterval(function(){c.resize_flag||s()},a.timer)}},F.addWidget({id:"stickyHeaders",priority:54,options:{stickyHeaders:"",stickyHeaders_appendTo:null,stickyHeaders_attachTo:null,stickyHeaders_xScroll:null,stickyHeaders_yScroll:null,stickyHeaders_offset:0,stickyHeaders_filteredToTop:!0,stickyHeaders_cloneId:"-sticky",stickyHeaders_addResizeEvent:!0,stickyHeaders_includeCaption:!0,stickyHeaders_zIndex:2},format:function(e,r,p){if(!(r.$table.hasClass("hasStickyHeaders")||0<=S.inArray("filter",r.widgets)&&!r.$table.hasClass("hasFilters"))){var t,a,s,i,h=r.$table,m=S(p.stickyHeaders_attachTo||p.stickyHeaders_appendTo),o=r.namespace+"stickyheaders ",b=S(p.stickyHeaders_yScroll||p.stickyHeaders_attachTo||z),n=S(p.stickyHeaders_xScroll||p.stickyHeaders_attachTo||z),l=h.children("thead:first").children("tr").not(".sticky-false").children(),y=h.children("tfoot"),c=R(0,p),v=h.parent().closest("."+F.css.table).hasClass("hasStickyHeaders")?h.parent().closest("table.tablesorter")[0].config.widgetOptions.$sticky.parent():[],w=v.length?v.height():0,d=p.$sticky=h.clone().addClass("containsStickyHeaders "+F.css.sticky+" "+p.stickyHeaders+" "+r.namespace.slice(1)+"_extra_table").wrap('<div class="'+F.css.stickyWrap+'">'),x=d.parent().addClass(F.css.stickyHide).css({position:m.length?"absolute":"fixed",padding:parseInt(d.parent().parent().css("padding-left"),10),top:c+w,left:0,visibility:"hidden",zIndex:p.stickyHeaders_zIndex||2}),f=d.children("thead:first"),C="",u=function(e,t){var r,a,s,i,o,n=e.filter(":visible"),l=n.length;for(r=0;r<l;r++)i=t.filter(":visible").eq(r),a="border-box"===(o=n.eq(r)).css("box-sizing")?o.outerWidth():"collapse"===i.css("border-collapse")?z.getComputedStyle?parseFloat(z.getComputedStyle(o[0],null).width):(s=parseFloat(o.css("border-width")),o.outerWidth()-parseFloat(o.css("padding-left"))-parseFloat(o.css("padding-right"))-s):o.width(),i.css({width:a,"min-width":a,"max-width":a})},_=function(e){return!1===e&&v.length?h.position().left:m.length?parseInt(m.css("padding-left"),10)||0:h.offset().left-parseInt(h.css("margin-left"),10)-S(z).scrollLeft()},$=function(){x.css({left:_(),width:h.outerWidth()}),u(h,d),u(l,i)},g=function(e){if(h.is(":visible")){w=v.length?v.offset().top-b.scrollTop()+v.height():0;var t,r=h.offset(),a=R(0,p),s=S.isWindow(b[0]),i=s?b.scrollTop():v.length?parseInt(v[0].style.top,10):b.offset().top,o=m.length?i:b.scrollTop(),n=p.stickyHeaders_includeCaption?0:h.children("caption").height()||0,l=o+a+w-n,c=h.height()-(x.height()+(y.height()||0))-n,d=l>r.top&&l<r.top+c?"visible":"hidden",f="visible"==d?F.css.stickyVis:F.css.stickyHide,u=!x.hasClass(f),g={visibility:d};m.length&&(u=!0,g.top=s?l-m.offset().top:m.scrollTop()),(t=_(s))!==parseInt(x.css("left"),10)&&(u=!0,g.left=t),g.top=(g.top||0)+(!s&&v.length?v.height():a+w),u&&x.removeClass(F.css.stickyVis+" "+F.css.stickyHide).addClass(f).css(g),d===C&&!e||($(),C=d)}};if(m.length&&!m.css("position")&&m.css("position","relative"),d.attr("id")&&(d[0].id+=p.stickyHeaders_cloneId),d.find("> thead:gt(0), tr.sticky-false").hide(),d.find("> tbody, > tfoot").remove(),d.find("caption").toggle(p.stickyHeaders_includeCaption),i=f.children().children(),d.css({height:0,width:0,margin:0}),i.find("."+F.css.resizer).remove(),h.addClass("hasStickyHeaders").bind("pagerComplete"+o,function(){$()}),F.bindEvents(e,f.children().children("."+F.css.header)),p.stickyHeaders_appendTo?S(p.stickyHeaders_appendTo).append(x):h.after(x),r.onRenderHeader)for(a=(s=f.children("tr").children()).length,t=0;t<a;t++)r.onRenderHeader.apply(s.eq(t),[t,r,d]);n.add(b).unbind("scroll resize ".split(" ").join(o).replace(/\s+/g," ")).bind("scroll resize ".split(" ").join(o),function(e){g("resize"===e.type)}),r.$table.unbind("stickyHeadersUpdate"+o).bind("stickyHeadersUpdate"+o,function(){g(!0)}),p.stickyHeaders_addResizeEvent&&F.addHeaderResizeEvent(e),h.hasClass("hasFilters")&&p.filter_columnFilters&&(h.bind("filterEnd"+o,function(){var e=S(document.activeElement).closest("td"),t=e.parent().children().index(e);x.hasClass(F.css.stickyVis)&&p.stickyHeaders_filteredToTop&&(z.scrollTo(0,h.position().top),0<=t&&r.$filters&&r.$filters.eq(t).find("a, select, input").filter(":visible").focus())}),F.filter.bindSearch(h,i.find("."+F.css.filter)),p.filter_hideFilters&&F.filter.hideFilters(r,d)),p.stickyHeaders_addResizeEvent&&h.bind("resize"+r.namespace+"stickyheaders",function(){$()}),g(!0),h.triggerHandler("stickyHeadersInit")}},remove:function(e,t,r){var a=t.namespace+"stickyheaders ";t.$table.removeClass("hasStickyHeaders").unbind("pagerComplete resize filterEnd stickyHeadersUpdate ".split(" ").join(a).replace(/\s+/g," ")).next("."+F.css.stickyWrap).remove(),r.$sticky&&r.$sticky.length&&r.$sticky.remove(),S(z).add(r.stickyHeaders_xScroll).add(r.stickyHeaders_yScroll).add(r.stickyHeaders_attachTo).unbind("scroll resize ".split(" ").join(a).replace(/\s+/g," ")),F.addHeaderResizeEvent(e,!0)}})}(e,window),function(d,t){"use strict";var f=d.tablesorter||{};d.extend(f.css,{resizableContainer:"tablesorter-resizable-container",resizableHandle:"tablesorter-resizable-handle",resizableNoSelect:"tablesorter-disableSelection",resizableStorage:"tablesorter-resizable"}),d(function(){var e="<style>body."+f.css.resizableNoSelect+" { -ms-user-select: none; -moz-user-select: -moz-none;-khtml-user-select: none; -webkit-user-select: none; user-select: none; }."+f.css.resizableContainer+" { position: relative; height: 1px; }."+f.css.resizableHandle+" { position: absolute; display: inline-block; width: 8px;top: 1px; cursor: ew-resize; z-index: 3; user-select: none; -moz-user-select: none; }</style>";d("head").append(e)}),f.resizable={init:function(e,t){if(!e.$table.hasClass("hasResizable")){e.$table.addClass("hasResizable");var r,a,s,i,o=e.$table,n=o.parent(),l=parseInt(o.css("margin-top"),10),c=t.resizable_vars={useStorage:f.storage&&!1!==t.resizable,$wrap:n,mouseXPosition:0,$target:null,$next:null,overflow:"auto"===n.css("overflow")||"scroll"===n.css("overflow")||"auto"===n.css("overflow-x")||"scroll"===n.css("overflow-x"),storedSizes:[]};for(f.resizableReset(e.table,!0),c.tableWidth=o.width(),c.fullWidth=Math.abs(n.width()-c.tableWidth)<20,c.useStorage&&c.overflow&&(f.storage(e.table,"tablesorter-table-original-css-width",c.tableWidth),i=f.storage(e.table,"tablesorter-table-resized-width")||"auto",f.resizable.setWidth(o,i,!0)),t.resizable_vars.storedSizes=s=(c.useStorage?f.storage(e.table,f.css.resizableStorage):[])||[],f.resizable.setWidths(e,t,s),f.resizable.updateStoredSizes(e,t),t.$resizable_container=d('<div class="'+f.css.resizableContainer+'">').css({top:l}).insertBefore(o),a=0;a<e.columns;a++)r=e.$headerIndexed[a],i=f.getColumnData(e.table,e.headers,a),"false"===f.getData(r,i,"resizable")||d('<div class="'+f.css.resizableHandle+'">').appendTo(t.$resizable_container).attr({"data-column":a,unselectable:"on"}).data("header",r).bind("selectstart",!1);f.resizable.bindings(e,t)}},updateStoredSizes:function(e,t){var r,a,s=e.columns,i=t.resizable_vars;for(i.storedSizes=[],r=0;r<s;r++)a=e.$headerIndexed[r],i.storedSizes[r]=a.is(":visible")?a.width():0},setWidth:function(e,t,r){e.css({width:t,"min-width":r?t:"","max-width":r?t:""})},setWidths:function(e,t,r){var a,s,i=t.resizable_vars,o=d(e.namespace+"_extra_headers"),n=e.$table.children("colgroup").children("col");if((r=r||i.storedSizes||[]).length){for(a=0;a<e.columns;a++)f.resizable.setWidth(e.$headerIndexed[a],r[a],i.overflow),o.length&&(s=o.eq(a).add(n.eq(a)),f.resizable.setWidth(s,r[a],i.overflow));(s=d(e.namespace+"_extra_table")).length&&!f.hasWidget(e.table,"scroller")&&f.resizable.setWidth(s,e.$table.outerWidth(),i.overflow)}},setHandlePosition:function(s,i){var o,n=s.$table.height(),e=i.$resizable_container.children(),l=Math.floor(e.width()/2);f.hasWidget(s.table,"scroller")&&(n=0,s.$table.closest("."+f.css.scrollerWrap).children().each(function(){var e=d(this);n+=e.filter('[style*="height"]').length?e.height():e.children("table").height()})),!i.resizable_includeFooter&&s.$table.children("tfoot").length&&(n-=s.$table.children("tfoot").height()),o=3.3<=parseFloat(d.fn.jquery)?0:s.$table.position().left,e.each(function(){var e=d(this),t=parseInt(e.attr("data-column"),10),r=s.columns-1,a=e.data("header");a&&(!a.is(":visible")||!i.resizable_addLastColumn&&f.resizable.checkVisibleColumns(s,t)?e.hide():(t<r||t===r&&i.resizable_addLastColumn)&&e.css({display:"inline-block",height:n,left:a.position().left-o+a.outerWidth()-l}))})},checkVisibleColumns:function(e,t){var r,a=0;for(r=t+1;r<e.columns;r++)a+=e.$headerIndexed[r].is(":visible")?1:0;return 0===a},toggleTextSelection:function(e,t,r){var a=e.namespace+"tsresize";t.resizable_vars.disabled=r,d("body").toggleClass(f.css.resizableNoSelect,r),r?d("body").attr("unselectable","on").bind("selectstart"+a,!1):d("body").removeAttr("unselectable").unbind("selectstart"+a)},bindings:function(i,o){var e=i.namespace+"tsresize";o.$resizable_container.children().bind("mousedown",function(e){var t,r=o.resizable_vars,a=d(i.namespace+"_extra_headers"),s=d(e.target).data("header");t=parseInt(s.attr("data-column"),10),r.$target=s=s.add(a.filter('[data-column="'+t+'"]')),r.target=t,r.$next=e.shiftKey||o.resizable_targetLast?s.parent().children().not(".resizable-false").filter(":last"):s.nextAll(":not(.resizable-false)").eq(0),t=parseInt(r.$next.attr("data-column"),10),r.$next=r.$next.add(a.filter('[data-column="'+t+'"]')),r.next=t,r.mouseXPosition=e.pageX,f.resizable.updateStoredSizes(i,o),f.resizable.toggleTextSelection(i,o,!0)}),d(document).bind("mousemove"+e,function(e){var t=o.resizable_vars;t.disabled&&0!==t.mouseXPosition&&t.$target&&(o.resizable_throttle?(clearTimeout(t.timer),t.timer=setTimeout(function(){f.resizable.mouseMove(i,o,e)},isNaN(o.resizable_throttle)?5:o.resizable_throttle)):f.resizable.mouseMove(i,o,e))}).bind("mouseup"+e,function(){o.resizable_vars.disabled&&(f.resizable.toggleTextSelection(i,o,!1),f.resizable.stopResize(i,o),f.resizable.setHandlePosition(i,o))}),d(t).bind("resize"+e+" resizeEnd"+e,function(){f.resizable.setHandlePosition(i,o)}),i.$table.bind("columnUpdate pagerComplete resizableUpdate ".split(" ").join(e+" "),function(){f.resizable.setHandlePosition(i,o)}).bind("resizableReset"+e,function(){f.resizableReset(i.table)}).find("thead:first").add(d(i.namespace+"_extra_table").find("thead:first")).bind("contextmenu"+e,function(){var e=0===o.resizable_vars.storedSizes.length;return f.resizableReset(i.table),f.resizable.setHandlePosition(i,o),o.resizable_vars.storedSizes=[],e})},mouseMove:function(e,t,r){if(0!==t.resizable_vars.mouseXPosition&&t.resizable_vars.$target){var a,s=0,i=t.resizable_vars,o=i.$next,n=i.storedSizes[i.target],l=r.pageX-i.mouseXPosition;if(i.overflow){if(0<n+l){for(i.storedSizes[i.target]+=l,f.resizable.setWidth(i.$target,i.storedSizes[i.target],!0),a=0;a<e.columns;a++)s+=i.storedSizes[a];f.resizable.setWidth(e.$table.add(d(e.namespace+"_extra_table")),s)}o.length||(i.$wrap[0].scrollLeft=e.$table.width())}else i.fullWidth?(i.storedSizes[i.target]+=l,i.storedSizes[i.next]-=l):i.storedSizes[i.target]+=l,f.resizable.setWidths(e,t);i.mouseXPosition=r.pageX,e.$table.triggerHandler("stickyHeadersUpdate")}},stopResize:function(e,t){var r=t.resizable_vars;f.resizable.updateStoredSizes(e,t),r.useStorage&&(f.storage(e.table,f.css.resizableStorage,r.storedSizes),f.storage(e.table,"tablesorter-table-resized-width",e.$table.width())),r.mouseXPosition=0,r.$target=r.$next=null,e.$table.triggerHandler("stickyHeadersUpdate"),e.$table.triggerHandler("resizableComplete")}},f.addWidget({id:"resizable",priority:40,options:{resizable:!0,resizable_addLastColumn:!1,resizable_includeFooter:!0,resizable_widths:[],resizable_throttle:!1,resizable_targetLast:!1},init:function(e,t,r,a){f.resizable.init(r,a)},format:function(e,t,r){f.resizable.setHandlePosition(t,r)},remove:function(e,t,r,a){if(r.$resizable_container){var s=t.namespace+"tsresize";t.$table.add(d(t.namespace+"_extra_table")).removeClass("hasResizable").children("thead").unbind("contextmenu"+s),r.$resizable_container.remove(),f.resizable.toggleTextSelection(t,r,!1),f.resizableReset(e,a),d(document).unbind("mousemove"+s+" mouseup"+s)}}}),f.resizableReset=function(i,o){d(i).each(function(){var e,t,r=this.config,a=r&&r.widgetOptions,s=a.resizable_vars;if(i&&r&&r.$headerIndexed.length){for(s.overflow&&s.tableWidth&&(f.resizable.setWidth(r.$table,s.tableWidth,!0),s.useStorage&&f.storage(i,"tablesorter-table-resized-width",s.tableWidth)),e=0;e<r.columns;e++)t=r.$headerIndexed[e],a.resizable_widths&&a.resizable_widths[e]?f.resizable.setWidth(t,a.resizable_widths[e],s.overflow):t.hasClass("resizable-false")||f.resizable.setWidth(t,"",s.overflow);r.$table.triggerHandler("stickyHeadersUpdate"),f.storage&&!o&&f.storage(this,f.css.resizableStorage,[])}})}}(e,window),function(r){"use strict";var c=r.tablesorter||{};function d(e){var t=c.storage(e.table,"tablesorter-savesort");return t&&t.hasOwnProperty("sortList")&&r.isArray(t.sortList)?t.sortList:[]}function f(e,t){return(t||d(e)).join(",")!==e.sortList.join(",")}c.addWidget({id:"saveSort",priority:20,options:{saveSort:!0},init:function(e,t,r,a){t.format(e,r,a,!0)},format:function(t,e,r,a){var s,i=e.$table,o=!1!==r.saveSort,n={sortList:e.sortList},l=c.debug(e,"saveSort");l&&(s=new Date),i.hasClass("hasSaveSort")?o&&t.hasInitialized&&c.storage&&f(e)&&(c.storage(t,"tablesorter-savesort",n),l&&console.log("saveSort >> Saving last sort: "+e.sortList+c.benchmark(s))):(i.addClass("hasSaveSort"),n="",c.storage&&(n=d(e),l&&console.log('saveSort >> Last sort loaded: "'+n+'"'+c.benchmark(s)),i.bind("saveSortReset",function(e){e.stopPropagation(),c.storage(t,"tablesorter-savesort","")})),a&&n&&0<n.length?e.sortList=n:t.hasInitialized&&n&&0<n.length&&f(e,n)&&c.sortOn(e,n))},remove:function(e,t){t.$table.removeClass("hasSaveSort"),c.storage&&c.storage(e,"tablesorter-savesort","")}})}(e),e.tablesorter});return jQuery;}));
