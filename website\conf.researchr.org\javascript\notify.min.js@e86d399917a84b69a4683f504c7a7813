/** Notify.js - v0.3.2 - 2015/08/27
 * http://notifyjs.com/
 * Copyright (c) 2015 <PERSON> - MIT
 */
!function(a,b,c,d){"use strict";var e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G=[].indexOf||function(a){for(var b=0,c=this.length;c>b;b++)if(b in this&&this[b]===a)return b;return-1};z="notify",y=z+"js",g=z+"!blank",B={t:"top",m:"middle",b:"bottom",l:"left",c:"center",r:"right"},r=["l","c","r"],F=["t","m","b"],v=["t","b","l","r"],w={t:"b",m:null,b:"t",l:"r",c:null,r:"l"},x=function(a){var b;return b=[],c.each(a.split(/\W+/),function(a,c){var d;return d=c.toLowerCase().charAt(0),B[d]?b.push(d):void 0}),b},E={},i={name:"core",html:'<div class="'+y+'-wrapper">\n  <div class="'+y+'-arrow"></div>\n  <div class="'+y+'-container"></div>\n</div>',css:"."+y+"-corner {\n  position: fixed;\n  margin: 5px;\n  z-index: 1050;\n}\n\n."+y+"-corner ."+y+"-wrapper,\n."+y+"-corner ."+y+"-container {\n  position: relative;\n  display: block;\n  height: inherit;\n  width: inherit;\n  margin: 3px;\n}\n\n."+y+"-wrapper {\n  z-index: 1;\n  position: absolute;\n  display: inline-block;\n  height: 0;\n  width: 0;\n}\n\n."+y+"-container {\n  display: none;\n  z-index: 1;\n  position: absolute;\n}\n\n."+y+"-hidable {\n  cursor: pointer;\n}\n\n[data-notify-text],[data-notify-html] {\n  position: relative;\n}\n\n."+y+"-arrow {\n  position: absolute;\n  z-index: 2;\n  width: 0;\n  height: 0;\n}"},D={"border-radius":["-webkit-","-moz-"]},p=function(a){return E[a]},f=function(b,d){var e,f,g,h;if(!b)throw"Missing Style name";if(!d)throw"Missing Style definition";if(!d.html)throw"Missing Style HTML";return(null!=(h=E[b])?h.cssElem:void 0)&&(a.console&&console.warn(""+z+": overwriting style '"+b+"'"),E[b].cssElem.remove()),d.name=b,E[b]=d,e="",d.classes&&c.each(d.classes,function(a,b){return e+="."+y+"-"+d.name+"-"+a+" {\n",c.each(b,function(a,b){return D[a]&&c.each(D[a],function(c,d){return e+="  "+d+a+": "+b+";\n"}),e+="  "+a+": "+b+";\n"}),e+="}\n"}),d.css&&(e+="/* styles for "+d.name+" */\n"+d.css),e&&(d.cssElem=u(e),d.cssElem.attr("id","notify-"+d.name)),g={},f=c(d.html),n("html",f,g),n("text",f,g),d.fields=g},u=function(a){var b;b=j("style"),b.attr("type","text/css"),c("head").append(b);try{b.html(a)}catch(d){b[0].styleSheet.cssText=a}return b},n=function(a,b,d){var e;return"html"!==a&&(a="text"),e="data-notify-"+a,m(b,"["+e+"]").each(function(){var b;return b=c(this).attr(e),b||(b=g),d[b]=a})},m=function(a,b){return a.is(b)?a:a.find(b)},A={clickToHide:!0,autoHide:!0,autoHideDelay:5e3,arrowShow:!0,arrowSize:5,breakNewLines:!0,elementPosition:"bottom",globalPosition:"top right",style:"bootstrap",className:"error",showAnimation:"slideDown",showDuration:400,hideAnimation:"slideUp",hideDuration:200,gap:5},t=function(a,b){var d;return d=function(){},d.prototype=a,c.extend(!0,new d,b)},k=function(a){return c.extend(A,a)},j=function(a){return c("<"+a+"></"+a+">")},q={},h=function(){return q={}},o=function(a){var b;return a.is("[type=radio]")&&(b=a.parents("form:first").find("[type=radio]").filter(function(b,d){return c(d).attr("name")===a.attr("name")}),a=b.first()),a},s=function(a,b,c){var e,f;if("string"==typeof c)c=parseInt(c,10);else if("number"!=typeof c)return;if(!isNaN(c))return e=B[w[b.charAt(0)]],f=b,a[e]!==d&&(b=B[e.charAt(0)],c=-c),a[b]===d?a[b]=c:a[b]+=c,null},C=function(a,b,c){if("l"===a||"t"===a)return 0;if("c"===a||"m"===a)return c/2-b/2;if("r"===a||"b"===a)return c-b;throw"Invalid alignment"},l=function(a){return l.e=l.e||j("div"),l.e.text(a).html()},e=function(){function a(a,b,d){"string"==typeof d&&(d={className:d}),this.options=t(A,c.isPlainObject(d)?d:{}),this.loadHTML(),this.wrapper=c(i.html),this.options.clickToHide&&this.wrapper.addClass(""+y+"-hidable"),this.wrapper.data(y,this),this.arrow=this.wrapper.find("."+y+"-arrow"),this.container=this.wrapper.find("."+y+"-container"),this.container.append(this.userContainer),a&&a.length&&(this.elementType=a.attr("type"),this.originalElement=a,this.elem=o(a),this.elem.data(y,this),this.elem.before(this.wrapper)),this.container.hide(),this.run(b)}return a.prototype.loadHTML=function(){var a;return a=this.getStyle(),this.userContainer=c(a.html),this.userFields=a.fields},a.prototype.show=function(a,b){var c,d,e,f,g,h=this;if(d=function(){return a||h.elem||h.destroy(),b?b():void 0},g=this.container.parent().parents(":hidden").length>0,e=this.container.add(this.arrow),c=[],g&&a)f="show";else if(g&&!a)f="hide";else if(!g&&a)f=this.options.showAnimation,c.push(this.options.showDuration);else{if(g||a)return d();f=this.options.hideAnimation,c.push(this.options.hideDuration)}return c.push(d),e[f].apply(e,c)},a.prototype.setGlobalPosition=function(){var a,b,d,e,f,g,h,i;return i=this.getPosition(),h=i[0],g=i[1],f=B[h],a=B[g],e=h+"|"+g,b=q[e],b||(b=q[e]=j("div"),d={},d[f]=0,"middle"===a?d.top="45%":"center"===a?d.left="45%":d[a]=0,b.css(d).addClass(""+y+"-corner"),c("body").append(b)),b.prepend(this.wrapper)},a.prototype.setElementPosition=function(){var a,b,d,e,f,g,h,i,j,k,l,m,n,o,p,q,t,u,x,y,z,A,D,E,H,I,J,K,L;for(D=this.getPosition(),y=D[0],u=D[1],x=D[2],l=this.elem.position(),i=this.elem.outerHeight(),m=this.elem.outerWidth(),j=this.elem.innerHeight(),k=this.elem.innerWidth(),E=this.wrapper.position(),f=this.container.height(),g=this.container.width(),o=B[y],q=w[y],t=B[q],h={},h[t]="b"===y?i:"r"===y?m:0,s(h,"top",l.top-E.top),s(h,"left",l.left-E.left),L=["top","left"],H=0,J=L.length;J>H;H++)z=L[H],p=parseInt(this.elem.css("margin-"+z),10),p&&s(h,z,p);if(n=Math.max(0,this.options.gap-(this.options.arrowShow?d:0)),s(h,t,n),this.options.arrowShow){for(d=this.options.arrowSize,b=c.extend({},h),a=this.userContainer.css("border-color")||this.userContainer.css("background-color")||"white",I=0,K=v.length;K>I;I++)z=v[I],A=B[z],z!==q&&(e=A===o?a:"transparent",b["border-"+A]=""+d+"px solid "+e);s(h,B[q],d),G.call(v,u)>=0&&s(b,B[u],2*d)}else this.arrow.hide();return G.call(F,y)>=0?(s(h,"left",C(u,g,m)),b&&s(b,"left",C(u,d,k))):G.call(r,y)>=0&&(s(h,"top",C(u,f,i)),b&&s(b,"top",C(u,d,j))),this.container.is(":visible")&&(h.display="block"),this.container.removeAttr("style").css(h),b?this.arrow.removeAttr("style").css(b):void 0},a.prototype.getPosition=function(){var a,b,c,d,e,f,g,h;if(b=this.options.position||(this.elem?this.options.elementPosition:this.options.globalPosition),a=x(b),0===a.length&&(a[0]="b"),c=a[0],G.call(v,c)<0)throw"Must be one of ["+v+"]";return(1===a.length||(d=a[0],G.call(F,d)>=0&&(e=a[1],G.call(r,e)<0))||(f=a[0],G.call(r,f)>=0&&(g=a[1],G.call(F,g)<0)))&&(a[1]=(h=a[0],G.call(r,h)>=0?"m":"l")),2===a.length&&(a[2]=a[1]),a},a.prototype.getStyle=function(a){var b;if(a||(a=this.options.style),a||(a="default"),b=E[a],!b)throw"Missing style: "+a;return b},a.prototype.updateClasses=function(){var a,b;return a=["base"],c.isArray(this.options.className)?a=a.concat(this.options.className):this.options.className&&a.push(this.options.className),b=this.getStyle(),a=c.map(a,function(a){return""+y+"-"+b.name+"-"+a}).join(" "),this.userContainer.attr("class",a)},a.prototype.run=function(a,b){var d,e,f,h,i,j=this;if(c.isPlainObject(b)?c.extend(this.options,b):"string"===c.type(b)&&(this.options.className=b),this.container&&!a)return void this.show(!1);if(this.container||a){e={},c.isPlainObject(a)?e=a:e[g]=a;for(f in e)d=e[f],h=this.userFields[f],h&&("text"===h&&(d=l(d),this.options.breakNewLines&&(d=d.replace(/\n/g,"<br/>"))),i=f===g?"":"="+f,m(this.userContainer,"[data-notify-"+h+i+"]").html(d));return this.updateClasses(),this.elem?this.setElementPosition():this.setGlobalPosition(),this.show(!0),this.options.autoHide?(clearTimeout(this.autohideTimer),this.autohideTimer=setTimeout(function(){return j.show(!1)},this.options.autoHideDelay)):void 0}},a.prototype.destroy=function(){return this.wrapper.remove()},a}(),c[z]=function(a,b,d){return a&&a.nodeName||a.jquery?c(a)[z](b,d):(d=b,b=a,new e(null,b,d)),a},c.fn[z]=function(a,b){return c(this).each(function(){var d;return d=o(c(this)).data(y),d?d.run(a,b):new e(c(this),a,b)}),this},c.extend(c[z],{defaults:k,addStyle:f,pluginOptions:A,getStyle:p,insertCSS:u,clearAnchors:h}),c(function(){return u(i.css).attr("id","core-notify"),c(b).on("click","."+y+"-hidable",function(a){return c(this).trigger("notify-hide")}),c(b).on("notify-hide","."+y+"-wrapper",function(a){var b;return null!=(b=c(this).data(y))?b.show(!1):void 0})})}(window,document,jQuery),$.notify.addStyle("bootstrap",{html:"<div>\n<span data-notify-text></span>\n</div>",classes:{base:{"font-weight":"bold",padding:"8px 15px 8px 14px","text-shadow":"0 1px 0 rgba(255, 255, 255, 0.5)","background-color":"#fcf8e3",border:"1px solid #fbeed5","border-radius":"4px","white-space":"nowrap","padding-left":"25px","background-repeat":"no-repeat","background-position":"3px 7px"},error:{color:"#B94A48","background-color":"#F2DEDE","border-color":"#EED3D7","background-image":"url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAtRJREFUeNqkVc1u00AQHq+dOD+0poIQfkIjalW0SEGqRMuRnHos3DjwAH0ArlyQeANOOSMeAA5VjyBxKBQhgSpVUKKQNGloFdw4cWw2jtfMOna6JOUArDTazXi/b3dm55socPqQhFka++aHBsI8GsopRJERNFlY88FCEk9Yiwf8RhgRyaHFQpPHCDmZG5oX2ui2yilkcTT1AcDsbYC1NMAyOi7zTX2Agx7A9luAl88BauiiQ/cJaZQfIpAlngDcvZZMrl8vFPK5+XktrWlx3/ehZ5r9+t6e+WVnp1pxnNIjgBe4/6dAysQc8dsmHwPcW9C0h3fW1hans1ltwJhy0GxK7XZbUlMp5Ww2eyan6+ft/f2FAqXGK4CvQk5HueFz7D6GOZtIrK+srupdx1GRBBqNBtzc2AiMr7nPplRdKhb1q6q6zjFhrklEFOUutoQ50xcX86ZlqaZpQrfbBdu2R6/G19zX6XSgh6RX5ubyHCM8nqSID6ICrGiZjGYYxojEsiw4PDwMSL5VKsC8Yf4VRYFzMzMaxwjlJSlCyAQ9l0CW44PBADzXhe7xMdi9HtTrdYjFYkDQL0cn4Xdq2/EAE+InCnvADTf2eah4Sx9vExQjkqXT6aAERICMewd/UAp/IeYANM2joxt+q5VI+ieq2i0Wg3l6DNzHwTERPgo1ko7XBXj3vdlsT2F+UuhIhYkp7u7CarkcrFOCtR3H5JiwbAIeImjT/YQKKBtGjRFCU5IUgFRe7fF4cCNVIPMYo3VKqxwjyNAXNepuopyqnld602qVsfRpEkkz+GFL1wPj6ySXBpJtWVa5xlhpcyhBNwpZHmtX8AGgfIExo0ZpzkWVTBGiXCSEaHh62/PoR0p/vHaczxXGnj4bSo+G78lELU80h1uogBwWLf5YlsPmgDEd4M236xjm+8nm4IuE/9u+/PH2JXZfbwz4zw1WbO+SQPpXfwG/BBgAhCNZiSb/pOQAAAAASUVORK5CYII=)"},success:{color:"#468847","background-color":"#DFF0D8","border-color":"#D6E9C6","background-image":"url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAutJREFUeNq0lctPE0Ecx38zu/RFS1EryqtgJFA08YCiMZIAQQ4eRG8eDGdPJiYeTIwHTfwPiAcvXIwXLwoXPaDxkWgQ6islKlJLSQWLUraPLTv7Gme32zoF9KSTfLO7v53vZ3d/M7/fIth+IO6INt2jjoA7bjHCJoAlzCRw59YwHYjBnfMPqAKWQYKjGkfCJqAF0xwZjipQtA3MxeSG87VhOOYegVrUCy7UZM9S6TLIdAamySTclZdYhFhRHloGYg7mgZv1Zzztvgud7V1tbQ2twYA34LJmF4p5dXF1KTufnE+SxeJtuCZNsLDCQU0+RyKTF27Unw101l8e6hns3u0PBalORVVVkcaEKBJDgV3+cGM4tKKmI+ohlIGnygKX00rSBfszz/n2uXv81wd6+rt1orsZCHRdr1Imk2F2Kob3hutSxW8thsd8AXNaln9D7CTfA6O+0UgkMuwVvEFFUbbAcrkcTA8+AtOk8E6KiQiDmMFSDqZItAzEVQviRkdDdaFgPp8HSZKAEAL5Qh7Sq2lIJBJwv2scUqkUnKoZgNhcDKhKg5aH+1IkcouCAdFGAQsuWZYhOjwFHQ96oagWgRoUov1T9kRBEODAwxM2QtEUl+Wp+Ln9VRo6BcMw4ErHRYjH4/B26AlQoQQTRdHWwcd9AH57+UAXddvDD37DmrBBV34WfqiXPl61g+vr6xA9zsGeM9gOdsNXkgpEtTwVvwOklXLKm6+/p5ezwk4B+j6droBs2CsGa/gNs6RIxazl4Tc25mpTgw/apPR1LYlNRFAzgsOxkyXYLIM1V8NMwyAkJSctD1eGVKiq5wWjSPdjmeTkiKvVW4f2YPHWl3GAVq6ymcyCTgovM3FzyRiDe2TaKcEKsLpJvNHjZgPNqEtyi6mZIm4SRFyLMUsONSSdkPeFtY1n0mczoY3BHTLhwPRy9/lzcziCw9ACI+yql0VLzcGAZbYSM5CCSZg1/9oc/nn7+i8N9p/8An4JMADxhH+xHfuiKwAAAABJRU5ErkJggg==)"},info:{color:"#3A87AD","background-color":"#D9EDF7","border-color":"#BCE8F1","background-image":"url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3QYFAhkSsdes/QAAA8dJREFUOMvVlGtMW2UYx//POaWHXg6lLaW0ypAtw1UCgbniNOLcVOLmAjHZolOYlxmTGXVZdAnRfXQm+7SoU4mXaOaiZsEpC9FkiQs6Z6bdCnNYruM6KNBw6YWewzl9z+sHImEWv+vz7XmT95f/+3/+7wP814v+efDOV3/SoX3lHAA+6ODeUFfMfjOWMADgdk+eEKz0pF7aQdMAcOKLLjrcVMVX3xdWN29/GhYP7SvnP0cWfS8caSkfHZsPE9Fgnt02JNutQ0QYHB2dDz9/pKX8QjjuO9xUxd/66HdxTeCHZ3rojQObGQBcuNjfplkD3b19Y/6MrimSaKgSMmpGU5WevmE/swa6Oy73tQHA0Rdr2Mmv/6A1n9w9suQ7097Z9lM4FlTgTDrzZTu4StXVfpiI48rVcUDM5cmEksrFnHxfpTtU/3BFQzCQF/2bYVoNbH7zmItbSoMj40JSzmMyX5qDvriA7QdrIIpA+3cdsMpu0nXI8cV0MtKXCPZev+gCEM1S2NHPvWfP/hL+7FSr3+0p5RBEyhEN5JCKYr8XnASMT0xBNyzQGQeI8fjsGD39RMPk7se2bd5ZtTyoFYXftF6y37gx7NeUtJJOTFlAHDZLDuILU3j3+H5oOrD3yWbIztugaAzgnBKJuBLpGfQrS8wO4FZgV+c1IxaLgWVU0tMLEETCos4xMzEIv9cJXQcyagIwigDGwJgOAtHAwAhisQUjy0ORGERiELgG4iakkzo4MYAxcM5hAMi1WWG1yYCJIcMUaBkVRLdGeSU2995TLWzcUAzONJ7J6FBVBYIggMzmFbvdBV44Corg8vjhzC+EJEl8U1kJtgYrhCzgc/vvTwXKSib1paRFVRVORDAJAsw5FuTaJEhWM2SHB3mOAlhkNxwuLzeJsGwqWzf5TFNdKgtY5qHp6ZFf67Y/sAVadCaVY5YACDDb3Oi4NIjLnWMw2QthCBIsVhsUTU9tvXsjeq9+X1d75/KEs4LNOfcdf/+HthMnvwxOD0wmHaXr7ZItn2wuH2SnBzbZAbPJwpPx+VQuzcm7dgRCB57a1uBzUDRL4bfnI0RE0eaXd9W89mpjqHZnUI5Hh2l2dkZZUhOqpi2qSmpOmZ64Tuu9qlz/SEXo6MEHa3wOip46F1n7633eekV8ds8Wxjn37Wl63VVa+ej5oeEZ/82ZBETJjpJ1Rbij2D3Z/1trXUvLsblCK0XfOx0SX2kMsn9dX+d+7Kf6h8o4AIykuffjT8L20LU+w4AZd5VvEPY+XpWqLV327HR7DzXuDnD8r+ovkBehJ8i+y8YAAAAASUVORK5CYII=)"},warn:{color:"#C09853","background-color":"#FCF8E3","border-color":"#FBEED5","background-image":"url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAMAAAC6V+0/AAABJlBMVEXr6eb/2oD/wi7/xjr/0mP/ykf/tQD/vBj/3o7/uQ//vyL/twebhgD/4pzX1K3z8e349vK6tHCilCWbiQymn0jGworr6dXQza3HxcKkn1vWvV/5uRfk4dXZ1bD18+/52YebiAmyr5S9mhCzrWq5t6ufjRH54aLs0oS+qD751XqPhAybhwXsujG3sm+Zk0PTwG6Shg+PhhObhwOPgQL4zV2nlyrf27uLfgCPhRHu7OmLgAafkyiWkD3l49ibiAfTs0C+lgCniwD4sgDJxqOilzDWowWFfAH08uebig6qpFHBvH/aw26FfQTQzsvy8OyEfz20r3jAvaKbhgG9q0nc2LbZxXanoUu/u5WSggCtp1anpJKdmFz/zlX/1nGJiYmuq5Dx7+sAAADoPUZSAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfdBgUBGhh4aah5AAAAlklEQVQY02NgoBIIE8EUcwn1FkIXM1Tj5dDUQhPU502Mi7XXQxGz5uVIjGOJUUUW81HnYEyMi2HVcUOICQZzMMYmxrEyMylJwgUt5BljWRLjmJm4pI1hYp5SQLGYxDgmLnZOVxuooClIDKgXKMbN5ggV1ACLJcaBxNgcoiGCBiZwdWxOETBDrTyEFey0jYJ4eHjMGWgEAIpRFRCUt08qAAAAAElFTkSuQmCC)"}}});