#!/usr/bin/env python3
"""
Command-line interface for XP Conference Scraper
"""

import argparse
import sys
import logging
from pathlib import Path
from main import XPConferenceScraper
import config

def setup_logging(level):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.LOG_FILE),
            logging.StreamHandler()
        ]
    )

def main():
    """Main command-line interface"""
    parser = argparse.ArgumentParser(
        description="XP Conference Scraper - Download conference files organized by day, session, and talk",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_scraper.py                           # Run with default settings
  python run_scraper.py --output my_downloads     # Custom output directory
  python run_scraper.py --workers 10              # Use 10 download threads
  python run_scraper.py --dry-run                 # Show what would be downloaded
  python run_scraper.py --log-level DEBUG         # Enable debug logging
        """
    )
    
    parser.add_argument(
        '--base-path',
        default=config.BASE_PATH,
        help=f'Path to website files (default: {config.BASE_PATH})'
    )
    
    parser.add_argument(
        '--output',
        default=config.OUTPUT_DIR,
        help=f'Output directory (default: {config.OUTPUT_DIR})'
    )
    
    parser.add_argument(
        '--workers',
        type=int,
        default=config.MAX_WORKERS,
        help=f'Number of download threads (default: {config.MAX_WORKERS})'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default=config.LOG_LEVEL,
        help=f'Logging level (default: {config.LOG_LEVEL})'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be downloaded without actually downloading'
    )
    
    parser.add_argument(
        '--metadata-only',
        action='store_true',
        help='Only generate metadata, skip downloads'
    )
    
    parser.add_argument(
        '--resume',
        action='store_true',
        help='Resume previous download (skip existing files)'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # Validate input paths
    base_path = Path(args.base_path)
    if not base_path.exists():
        logger.error(f"Base path does not exist: {base_path}")
        sys.exit(1)
    
    program_file = base_path / config.PROGRAM_FILE
    if not program_file.exists():
        logger.error(f"Program file not found: {program_file}")
        sys.exit(1)
    
    # Create scraper instance
    scraper = XPConferenceScraper(
        base_path=str(base_path),
        output_dir=args.output
    )
    
    try:
        logger.info(f"Starting XP Conference scraper...")
        logger.info(f"Base path: {base_path}")
        logger.info(f"Output directory: {args.output}")
        logger.info(f"Workers: {args.workers}")
        
        # Load and parse main program
        soup = scraper.load_main_program()
        logger.info("Loaded main program file")
        
        # Extract days and sessions
        days = scraper.extract_days(soup)
        logger.info(f"Found {len(days)} conference days")
        
        # Find detail pages for talks
        scraper.find_detail_pages(days)
        
        # Process attachments
        scraper.process_attachments(days)
        
        # Show summary
        total_sessions = sum(len(day.sessions) for day in days)
        total_talks = sum(len(session.talks) for day in days for session in day.sessions)
        total_attachments = sum(len(talk.attachments) for day in days for session in day.sessions for talk in session.talks)
        
        logger.info(f"Summary: {len(days)} days, {total_sessions} sessions, {total_talks} talks, {total_attachments} attachments")
        
        if args.dry_run:
            logger.info("DRY RUN - Showing what would be downloaded:")
            for day in days:
                for session in day.sessions:
                    for talk in session.talks:
                        if talk.has_attachments:
                            logger.info(f"  {day.date} / {session.title} / {talk.title}: {len(talk.attachments)} files")
            logger.info("DRY RUN completed")
            return
        
        # Download attachments (unless metadata-only)
        if not args.metadata_only:
            scraper.download_all_attachments(days, max_workers=args.workers)
        
        # Save metadata
        scraper.save_metadata(days)
        
        logger.info("Scraping completed successfully!")
        
        # Final summary
        for day in days:
            sessions_with_attachments = sum(1 for session in day.sessions 
                                          if any(talk.has_attachments for talk in session.talks))
            logger.info(f"  {day.date}: {len(day.sessions)} sessions, {sessions_with_attachments} with attachments")
        
    except KeyboardInterrupt:
        logger.warning("Scraping interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        if args.log_level == 'DEBUG':
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
