/*Printable program*/
@media print {
 	#messages-placeholder, .btn, .facet-placeholder, .blog-link, .logobar, .panel, .nav-tabs, .nav-pills, td a>.glyphicon, footer, .page-header a, .page-header .text-muted.small , .publication-link, .scroll-to-now-btn, .scroll-to-upcoming-btn, .auto-scroll, .auto-scroll-upcoming, #maintenance-message, .control-time-zone{
		display:none !important;
	}
	a[href]:after {
	    content: none !important;
	}
  table.session-table, table.no-bottom-margin{
		page-break-inside: avoid !important;
		-webkit-print-color-adjust: exact;
	}
	.day-wrapper {
    page-break-after: always;
  }
	table.no-bottom-margin{
		page-break-after: avoid !important;
	}
	.frame{
	    display: block !important;
	}
	#Detailed-Table{
		display: block !important;
    visibility: visible !important;
	}
	tr.session-details>td, .track-color{
		padding: 0px !important;
	}
	.track-color{
		border: 5px solid #ddd;
	}
	td{
		padding: 2px 5px !important;
	}
	tr.session-details td{
		background-color: #ddd !important;
		padding: 5px !important;
	}
	.session-table{
		margin-bottom: 0px;
	}
	.performers{
		padding-left: 20px;
		font-style: italic;
	}
	.room-link{
	    border: 1px solid black;
	    padding: 0px 11px;
      white-space: nowrap;
	}
	.output-badge img{
		 max-width: 40px !important;
	}
	.day-header{
	  -webkit-print-color-adjust: exact;
	}
	.hide-in-print{
	  display: none !important;
	}
	.slot-label{
/* 		color: initial !important; */
		background: inherit !important;
	}
}