# XP Conference Scraper

A Python scraper for the XP 2025 conference website that downloads all attached files organized by day, session, and talk.

## Features

- **Structured Organization**: Files are organized in a hierarchical folder structure:
  ```
  output_data/
  ├── Mon_2_Jun/
  │   ├── 09:00-12:30_Session_Name/
  │   │   ├── Talk_Title_1/
  │   │   │   ├── presentation.pdf
  │   │   │   └── slides.pptx
  │   │   └── Talk_Title_2/
  │   │       └── paper.pdf
  │   └── 14:00-17:30_Another_Session/
  │       └── ...
  ├── Tue_3_Jun/
  │   └── ...
  └── conference_metadata.json
  ```

- **Multi-threaded Downloads**: Parallel downloading for improved performance
- **Progress Tracking**: Real-time progress bar showing download status
- **Robust Error Handling**: Continues operation even if some downloads fail
- **Metadata Export**: Saves complete conference structure to JSON
- **Resume Capability**: <PERSON>ps already downloaded files

## Requirements

- Python 3.7+
- Required packages (install with `pip install -r requirements.txt`):
  - beautifulsoup4>=4.12.0
  - requests>=2.31.0
  - tqdm>=4.66.0
  - lxml>=4.9.0

## Installation

1. <PERSON>lone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Basic Usage

```bash
python main.py
```

This will:
1. Parse the main program HTML file from `website/conf.researchr.org/program/xp-2025/program-xp-2025/index.html`
2. Find all detail pages in `website/conf.researchr.org/details/`
3. Extract file attachments from each talk
4. Download all files to the `output_data/` directory
5. Generate a metadata JSON file with the complete conference structure

### Configuration

You can customize the scraper by modifying the `XPConferenceScraper` initialization:

```python
scraper = XPConferenceScraper(
    base_path="website/conf.researchr.org",  # Path to downloaded website files
    output_dir="output_data"                 # Output directory for downloads
)
```

### Advanced Usage

To modify download behavior, you can adjust these parameters in the `download_all_attachments` method:

```python
self.download_all_attachments(days, max_workers=5)  # Number of concurrent downloads
```

## File Structure

### Input Files

The scraper expects the following file structure:

```
website/conf.researchr.org/
├── program/xp-2025/program-xp-2025/index.html  # Main program page
└── details/                                    # Detail pages directory
    └── xp-2025/
        ├── workshop1/
        │   └── talk1.html
        └── workshop2/
            └── talk2.html
```

### Output Structure

Files are organized as follows:

- **Day folders**: Named after conference days (e.g., "Mon_2_Jun")
- **Session folders**: Named with time slot and session title (e.g., "14_00_-_17_30_Session_Name")
- **Talk folders**: Named after individual talk titles
- **Files**: Original filenames are preserved within talk folders

## Logging

The scraper provides detailed logging:

- **Console output**: Real-time progress and status updates
- **Log file**: Complete log saved to `scraper.log`
- **Progress bar**: Visual progress indicator during downloads

## Error Handling

The scraper handles various error conditions:

- **Missing files**: Continues if some HTML files are missing
- **Download failures**: Creates placeholder files with error information
- **Network issues**: Retries and logs failed downloads
- **Invalid filenames**: Sanitizes filenames for filesystem compatibility

## Metadata

The scraper generates a comprehensive metadata file (`conference_metadata.json`) containing:

- Conference structure (days, sessions, talks)
- File attachment information
- Download statistics
- Timestamp of scraping operation

## Limitations

1. **Authentication**: The scraper cannot handle authenticated downloads
2. **JavaScript**: Complex JavaScript-based downloads may not work
3. **Rate Limiting**: May be blocked by server rate limiting
4. **Dynamic Content**: Only works with static HTML files

## Troubleshooting

### Common Issues

1. **503 Server Errors**: The conference website may block automated downloads
   - Solution: The scraper will create placeholder files with error information

2. **Missing Detail Pages**: Some talks may not have detail pages
   - Solution: Only talks with detail pages will be processed

3. **Invalid Characters in Filenames**: Some talk titles may contain invalid characters
   - Solution: The scraper automatically sanitizes filenames

### Debug Mode

To enable debug logging, modify the logging configuration:

```python
logging.basicConfig(level=logging.DEBUG, ...)
```

## Contributing

To extend the scraper:

1. **Add new parsers**: Extend the `extract_attachments_from_detail_page` method
2. **Improve URL parsing**: Enhance the `parse_download_url` method
3. **Add new output formats**: Modify the `save_metadata` method

## License

This project is for educational and research purposes. Please respect the conference website's terms of service and robots.txt file.
