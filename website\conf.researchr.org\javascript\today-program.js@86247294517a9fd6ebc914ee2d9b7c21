//scroll to next auto-scrollable element
function autoScroll( cls ){
  var elem = $('.' + cls + ':visible');
  if(elem.length){
    var scrollContainer = $('body');
    var maxScroll = scrollContainer[0].scrollHeight - scrollContainer.outerHeight()
    var elemFocusTop = Math.min( maxScroll, ( $(elem).offset().top - 120 ) );
    var newTop = Math.abs( $('html').scrollTop() - elemFocusTop ) < 50 ? $('.navbar').offset().top : elemFocusTop;
    //disable smooth scrolling, it causes laggy scrolling after the animation on mobile devices
    $('html').css('scroll-behavior', 'auto');
    
    $('body,html').animate({
      scrollTop: newTop
    }, 200);
  }
  hideToolTips();
}

function hidePastEventsBtnAction(){
  var facetph = $('.facet-placeholder[data-facet-type="past"]');
  var facetBtn = facetph.find('.facet-false');
  if(facetBtn.length){
    facetBtn.click();
  } else{
    facetBtn = facetph.find('.facet-true');
    if(facetBtn.length){
      //it's already selected, re-apply filter to hide elements that are promoted to past event by the client
      filterFacets();
    }
  }
  //hide floating button and tooltip after action
  $('.hide-past-events-btn').hide();
  hideToolTips();
}

function hideToolTips(){ $('.tooltip.fade.top.in, .ui-tooltip-content').remove() }

//track elements during time
function initTrackedElems(){
  $('.nowable').each( addTracked );
  updateTimes();
}

var trackedElemData = [];
var nowElemData = [];
var upComingElemData = [];
var nowClass = "auto-scroll";
var upcomingClass = "auto-scroll-upcoming";
var minUpcomingHours = 25;
var updateIntervalMS = 30000;
var nowEpochMS = (new Date).getTime()+updateIntervalMS;
var upcomingBtnVisible = false;

function addTracked(idx, el){
  var data ={
    elem : el,
    start : el.dataset.start,
    end : el.dataset.end
  };
//  if(data.end == undefined || nowEpochMS < data.end){
    trackedElemData.push( data );
//  }
}

//updates the floating now and next labels, interacts with faceted search
function updateTimes(){
  var pastElemParentTables = [];
  nowEpochMS = (new Date).getTime();
  nowElemData = nowElemData.filter( function(obj){
    var keep = ( obj.end == undefined || obj.end > nowEpochMS );
    if( !keep ){
      var wrapperElem = $(obj.elem).closest('table.session-table, .day-wrapper')
      if( wrapperElem.length && wrapperElem.data('facet-past') ){
        pastElemParentTables.push(wrapperElem.get(0));
      }
      obj.elem.classList.remove( nowClass );
    }
    return keep;
  });
  
  
  var lowestStart = Number.MAX_SAFE_INTEGER;
  var newUpComingElems = [];
  
  //update array of tracked elements, filter out expired elements
  trackedElemData = trackedElemData.filter( function(obj){
    var keep = ( obj.end == undefined || obj.end > nowEpochMS );
    if(!keep){
      var wrapperElem = $(obj.elem).closest('table.session-table, .day-wrapper');
      if( wrapperElem.length && wrapperElem.data('facet-past') ){
        pastElemParentTables.push(wrapperElem.get(0));
      }
    } else{
      if( obj.start == undefined || obj.start < nowEpochMS ){
        nowElemData.push( obj );
        obj.elem.classList.add( nowClass );
        keep = false;
      } else{
        //only consider visible data, and only data within a table col (TD), so time gaps between sessions are not considered in upcoming events
        if(obj.start !== undefined && obj.end !== undefined && obj.start <= lowestStart && isObjectVisible(obj) && obj.elem.parentElement.nodeName == "TD" ){
          if(obj.start == lowestStart){
            newUpComingElems.push( obj );
          } else{
            if( (obj.start - nowEpochMS) < minUpcomingHours*60*60*1000 ){
              lowestStart = obj.start;
              newUpComingElems = [obj];
            }
          }
        }
      }
    }
    return keep;
  });
  
  //update upcoming button text
  var remaining = ""
  if(newUpComingElems.length){
    var ms = lowestStart-nowEpochMS;
    if(ms < (60*1000)){
      remaining += "< 1 min"
    }
    if( ms > (60*60*1000)){
      remaining += Math.floor(ms/(60*60*1000)) + 'h '
      ms = ms % (60*60*1000);
    }
    if(ms > (60*1000)){
      remaining += Math.round(ms/(60*1000)) + ' min '
      ms = ms % 60*1000;
    }
    $('#upcoming-btn-text').text("next event in " + remaining);
    
  }
  toggleUpcomingButton(newUpComingElems.length > 0);
  
  //drop upcomingClass from elements not in the upcoming elements anymore
  upComingElemData.forEach( function(obj){
    if(newUpComingElems.indexOf(obj) < 0){
      obj.elem.classList.remove( upcomingClass );
      obj.elem.innerHTML = "";
    }
  });
  //add upcomingClass to the upcoming elements
  newUpComingElems.forEach( function(obj){
    obj.elem.classList.add( upcomingClass );
    obj.elem.innerHTML = "in " + remaining;
  });
  upComingElemData = newUpComingElems;
  
  clearTimeout(window.updateTimesTimer);
  window.updateTimesTimer = setTimeout(updateTimes, updateIntervalMS);
  
  //Update facet data and show hide-past-events-button when elements became 'past event'
  if(pastElemParentTables.length && typeof updateFacets == 'function'){
    var facetsNeedUpdate = false;
    var hasNoOngoingEventSelector = ':not(:has(.' + nowClass + '))';
    pastElemParentTables = $( pastElemParentTables ).filter( hasNoOngoingEventSelector );
    pastElemParentTables.each( function(){
      this.removeAttribute('data-facet-past');
      facetsNeedUpdate = true;
    });
    
    if( facetsNeedUpdate ){
      $('.hide-past-events-btn').show();
      updateFacets();
    }
  }
}

function toggleUpcomingButton( show ){
  if( (show && true) != (upcomingBtnVisible && true)){
    $('.scroll-to-upcoming-btn').toggle(show);
    upcomingBtnVisible = show;
  }
}

function isObjectVisible( obj ){ return  obj.elem.offsetParent != null; }

$( document ).ready( function(){
  //on update facets, try to update now and upcoming labels to apply to filtered program
  if(typeof updateFacets == 'function'){
    var origFacets = updateFacets;
    updateFacets = function(){
      origFacets();
      updateTimes();
    }
  }
  $('a[data-toggle="tab"]').on('shown.bs.tab', updateTimes );
} );


function isTouchDevice(){
  return 'ontouchstart' in document.documentElement;
}

if(isTouchDevice()){
  $(window).on('load', function(){
    var visibleTooltipElems = $('.tooltip-on-load:visible');
    setTimeout(function(){ visibleTooltipElems.tooltip().mouseover(); }, 1000);
    setTimeout(function(){ visibleTooltipElems.tooltip('hide'); }, 3000);
  });
}