#!/usr/bin/env python3
"""
Test script for XP Conference Scraper
"""

import os
import json
import logging
from pathlib import Path
from main import XPConferenceScraper

def test_scraper():
    """Test the scraper functionality"""
    print("Testing XP Conference Scraper...")
    
    # Create a test scraper instance
    scraper = XPConferenceScraper(output_dir="test_output")
    
    try:
        # Test loading the main program
        soup = scraper.load_main_program()
        print("✓ Successfully loaded main program file")
        
        # Test extracting days
        days = scraper.extract_days(soup)
        print(f"✓ Found {len(days)} conference days")
        
        # Test finding detail pages
        scraper.find_detail_pages(days)
        detail_pages_found = sum(1 for day in days for session in day.sessions for talk in session.talks if talk.detail_url)
        print(f"✓ Found {detail_pages_found} detail pages")
        
        # Test processing attachments
        scraper.process_attachments(days)
        total_attachments = sum(len(talk.attachments) for day in days for session in day.sessions for talk in session.talks)
        print(f"✓ Found {total_attachments} file attachments")
        
        # Test metadata generation
        scraper.save_metadata(days)
        metadata_file = Path("test_output/conference_metadata.json")
        if metadata_file.exists():
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            print(f"✓ Generated metadata with {metadata['total_talks']} talks")
        
        # Print summary
        print("\nSummary:")
        for day in days:
            sessions_with_attachments = sum(1 for session in day.sessions 
                                          if any(talk.has_attachments for talk in session.talks))
            print(f"  {day.date}: {len(day.sessions)} sessions, {sessions_with_attachments} with attachments")
        
        print("\n✓ All tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False
    
    finally:
        # Clean up test output
        import shutil
        if Path("test_output").exists():
            shutil.rmtree("test_output")
            print("✓ Cleaned up test files")

def test_file_structure():
    """Test that required files exist"""
    print("\nTesting file structure...")
    
    required_files = [
        "website/conf.researchr.org/program/xp-2025/program-xp-2025/index.html",
        "website/conf.researchr.org/details"
    ]
    
    for file_path in required_files:
        path = Path(file_path)
        if path.exists():
            print(f"✓ Found: {file_path}")
        else:
            print(f"✗ Missing: {file_path}")
            return False
    
    return True

def test_dependencies():
    """Test that all required dependencies are installed"""
    print("\nTesting dependencies...")
    
    required_packages = [
        ("beautifulsoup4", "bs4"),
        ("requests", "requests"),
        ("tqdm", "tqdm"),
        ("lxml", "lxml")
    ]

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name} is installed")
        except ImportError:
            print(f"✗ {package_name} is not installed")
            return False
    
    return True

if __name__ == "__main__":
    print("XP Conference Scraper Test Suite")
    print("=" * 40)
    
    # Run all tests
    tests_passed = 0
    total_tests = 3
    
    if test_dependencies():
        tests_passed += 1
    
    if test_file_structure():
        tests_passed += 1
    
    if test_scraper():
        tests_passed += 1
    
    print(f"\nTest Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The scraper is ready to use.")
    else:
        print("❌ Some tests failed. Please check the requirements and file structure.")
